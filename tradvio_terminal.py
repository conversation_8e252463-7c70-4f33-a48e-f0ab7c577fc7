import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import yfinance as yf
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
from datetime import datetime, timedelta
import threading
import time
import warnings
warnings.filterwarnings('ignore')

# Set dark theme for matplotlib
plt.style.use('dark_background')

class TradingTerminalApp:
    def __init__(self, root):
        self.root = root
        self.root.title("TRADVIO - Professional Cryptocurrency Trading Terminal")
        self.root.geometry("1920x1080")
        self.root.state('zoomed')  # Maximize window
        self.root.configure(bg='#0a0a0a')
        
        # Trading terminal colors
        self.colors = {
            'bg_primary': '#0a0a0a',
            'bg_secondary': '#1a1a1a', 
            'bg_tertiary': '#2a2a2a',
            'text_primary': '#ffffff',
            'text_secondary': '#cccccc',
            'accent_green': '#00ff88',
            'accent_red': '#ff4444',
            'accent_blue': '#4488ff',
            'accent_yellow': '#ffaa00',
            'border': '#333333'
        }
        
        # Data storage
        self.models = {}
        self.data_cache = {}
        self.real_time_data = {}
        self.is_training = False
        self.is_streaming = False
        
        # Cryptocurrency configuration
        self.COINS = {
            'BTC': {'symbol': 'BTC-USD', 'name': 'Bitcoin', 'color': '#f7931a'},
            'ETH': {'symbol': 'ETH-USD', 'name': 'Ethereum', 'color': '#627eea'},
            'SOL': {'symbol': 'SOL-USD', 'name': 'Solana', 'color': '#9945ff'},
            'ADA': {'symbol': 'ADA-USD', 'name': 'Cardano', 'color': '#0033ad'},
            'XRP': {'symbol': 'XRP-USD', 'name': 'XRP', 'color': '#23292f'}
        }
        
        self.selected_coin = 'BTC'
        self.setup_styles()
        self.setup_ui()
        self.start_real_time_updates()
        
    def setup_styles(self):
        """Setup custom styles for professional look"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Dark.TFrame', background=self.colors['bg_secondary'])
        style.configure('Dark.TLabel', background=self.colors['bg_secondary'], 
                       foreground=self.colors['text_primary'], font=('Consolas', 10))
        style.configure('Dark.TButton', background=self.colors['bg_tertiary'],
                       foreground=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        style.configure('Header.TLabel', background=self.colors['bg_primary'],
                       foreground=self.colors['accent_blue'], font=('Consolas', 12, 'bold'))
        style.configure('Price.TLabel', background=self.colors['bg_secondary'],
                       foreground=self.colors['accent_green'], font=('Consolas', 14, 'bold'))
        style.configure('Alert.TLabel', background=self.colors['bg_secondary'],
                       foreground=self.colors['accent_red'], font=('Consolas', 10, 'bold'))
        
    def setup_ui(self):
        """Setup Bloomberg-style trading terminal interface"""
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Top header bar
        self.setup_header(main_frame)
        
        # Main content area
        content_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(2, 0))
        
        # Left panel - Market data and controls
        left_panel = tk.Frame(content_frame, bg=self.colors['bg_secondary'], width=350)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 2))
        left_panel.pack_propagate(False)
        
        # Center panel - Main chart
        center_panel = tk.Frame(content_frame, bg=self.colors['bg_secondary'])
        center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2))
        
        # Right panel - Analysis and predictions
        right_panel = tk.Frame(content_frame, bg=self.colors['bg_secondary'], width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)
        
        # Setup panels
        self.setup_left_panel(left_panel)
        self.setup_center_panel(center_panel)
        self.setup_right_panel(right_panel)
        
    def setup_header(self, parent):
        """Setup header with logo and real-time info"""
        header_frame = tk.Frame(parent, bg=self.colors['bg_primary'], height=60)
        header_frame.pack(fill=tk.X, pady=(0, 2))
        header_frame.pack_propagate(False)
        
        # Logo and title
        title_frame = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        title_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        ttk.Label(title_frame, text="TRADVIO", style='Header.TLabel',
                 font=('Consolas', 20, 'bold')).pack(anchor=tk.W)
        ttk.Label(title_frame, text="Professional Cryptocurrency Trading Terminal",
                 style='Dark.TLabel', font=('Consolas', 10)).pack(anchor=tk.W)
        
        # Real-time clock and status
        status_frame = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        status_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=10)
        
        self.clock_label = ttk.Label(status_frame, text="", style='Header.TLabel',
                                    font=('Consolas', 14, 'bold'))
        self.clock_label.pack(anchor=tk.E)
        
        self.status_label = ttk.Label(status_frame, text="● LIVE", style='Dark.TLabel',
                                     font=('Consolas', 10))
        self.status_label.pack(anchor=tk.E)
        
        self.update_clock()

    def setup_left_panel(self, parent):
        """Setup left panel with market data and controls"""
        # Market overview
        market_frame = tk.LabelFrame(parent, text="MARKET OVERVIEW", bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        market_frame.pack(fill=tk.X, padx=5, pady=5)

        self.market_data = {}
        for coin_id, coin_info in self.COINS.items():
            coin_frame = tk.Frame(market_frame, bg=self.colors['bg_secondary'])
            coin_frame.pack(fill=tk.X, pady=2)

            # Coin selector button
            btn = tk.Button(coin_frame, text=coin_id, bg=coin_info['color'], fg='white',
                           font=('Consolas', 10, 'bold'), width=5,
                           command=lambda c=coin_id: self.select_coin(c))
            btn.pack(side=tk.LEFT, padx=(0, 5))

            # Price and change labels
            price_label = ttk.Label(coin_frame, text="$0.00", style='Price.TLabel')
            price_label.pack(side=tk.LEFT, padx=(0, 5))

            change_label = ttk.Label(coin_frame, text="0.00%", style='Dark.TLabel')
            change_label.pack(side=tk.LEFT)

            self.market_data[coin_id] = {
                'price_label': price_label,
                'change_label': change_label,
                'button': btn
            }

        # Trading controls
        controls_frame = tk.LabelFrame(parent, text="TRADING CONTROLS", bg=self.colors['bg_secondary'],
                                     fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        controls_frame.pack(fill=tk.X, padx=5, pady=5)

        # Parameters
        ttk.Label(controls_frame, text="Window Size:", style='Dark.TLabel').pack(anchor=tk.W, padx=5)
        self.window_var = tk.IntVar(value=60)
        window_scale = tk.Scale(controls_frame, from_=30, to=120, variable=self.window_var,
                               orient=tk.HORIZONTAL, bg=self.colors['bg_tertiary'],
                               fg=self.colors['text_primary'], highlightthickness=0)
        window_scale.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(controls_frame, text="Training Iterations:", style='Dark.TLabel').pack(anchor=tk.W, padx=5)
        self.epochs_var = tk.IntVar(value=100)
        epochs_scale = tk.Scale(controls_frame, from_=50, to=200, variable=self.epochs_var,
                               orient=tk.HORIZONTAL, bg=self.colors['bg_tertiary'],
                               fg=self.colors['text_primary'], highlightthickness=0)
        epochs_scale.pack(fill=tk.X, padx=5, pady=2)

        # Action buttons
        btn_frame = tk.Frame(controls_frame, bg=self.colors['bg_secondary'])
        btn_frame.pack(fill=tk.X, padx=5, pady=5)

        self.predict_btn = tk.Button(btn_frame, text="🚀 ANALYZE", bg=self.colors['accent_blue'],
                                    fg='white', font=('Consolas', 12, 'bold'),
                                    command=self.start_analysis)
        self.predict_btn.pack(fill=tk.X, pady=2)

        self.stream_btn = tk.Button(btn_frame, text="📡 LIVE STREAM", bg=self.colors['accent_green'],
                                   fg='white', font=('Consolas', 12, 'bold'),
                                   command=self.toggle_streaming)
        self.stream_btn.pack(fill=tk.X, pady=2)

        # Log terminal
        log_frame = tk.LabelFrame(parent, text="TERMINAL LOG", bg=self.colors['bg_secondary'],
                                fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=40,
                                                 bg=self.colors['bg_primary'],
                                                 fg=self.colors['text_primary'],
                                                 font=('Consolas', 9),
                                                 insertbackground=self.colors['text_primary'])
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_center_panel(self, parent):
        """Setup center panel with main chart"""
        # Chart header
        chart_header = tk.Frame(parent, bg=self.colors['bg_secondary'], height=40)
        chart_header.pack(fill=tk.X, padx=5, pady=(5, 0))
        chart_header.pack_propagate(False)

        self.chart_title = ttk.Label(chart_header, text="BTC/USD - BITCOIN",
                                    style='Header.TLabel', font=('Consolas', 16, 'bold'))
        self.chart_title.pack(side=tk.LEFT, padx=10, pady=10)

        self.chart_price = ttk.Label(chart_header, text="$0.00",
                                    style='Price.TLabel', font=('Consolas', 18, 'bold'))
        self.chart_price.pack(side=tk.RIGHT, padx=10, pady=10)

        # Main chart area
        chart_frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create matplotlib figure with dark theme
        self.fig = Figure(figsize=(12, 8), facecolor=self.colors['bg_secondary'])
        self.fig.patch.set_facecolor(self.colors['bg_secondary'])

        # Create subplots
        gs = self.fig.add_gridspec(3, 1, height_ratios=[3, 1, 1], hspace=0.3)
        self.ax_price = self.fig.add_subplot(gs[0])
        self.ax_volume = self.fig.add_subplot(gs[1])
        self.ax_indicators = self.fig.add_subplot(gs[2])

        # Style the axes
        for ax in [self.ax_price, self.ax_volume, self.ax_indicators]:
            ax.set_facecolor(self.colors['bg_primary'])
            ax.tick_params(colors=self.colors['text_secondary'])
            ax.spines['bottom'].set_color(self.colors['border'])
            ax.spines['top'].set_color(self.colors['border'])
            ax.spines['right'].set_color(self.colors['border'])
            ax.spines['left'].set_color(self.colors['border'])

        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def setup_right_panel(self, parent):
        """Setup right panel with analysis and predictions"""
        # Current position
        position_frame = tk.LabelFrame(parent, text="CURRENT POSITION", bg=self.colors['bg_secondary'],
                                     fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        position_frame.pack(fill=tk.X, padx=5, pady=5)

        self.position_labels = {}
        position_data = [
            ('Asset:', 'BTC'),
            ('Quantity:', '0.00'),
            ('Avg Price:', '$0.00'),
            ('Current Value:', '$0.00'),
            ('P&L:', '$0.00 (0.00%)')
        ]

        for label, value in position_data:
            row = tk.Frame(position_frame, bg=self.colors['bg_secondary'])
            row.pack(fill=tk.X, padx=5, pady=2)

            ttk.Label(row, text=label, style='Dark.TLabel').pack(side=tk.LEFT)
            value_label = ttk.Label(row, text=value, style='Price.TLabel')
            value_label.pack(side=tk.RIGHT)
            self.position_labels[label] = value_label

        # AI Predictions
        prediction_frame = tk.LabelFrame(parent, text="AI PREDICTIONS", bg=self.colors['bg_secondary'],
                                       fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        prediction_frame.pack(fill=tk.X, padx=5, pady=5)

        self.prediction_labels = {}
        prediction_data = [
            ('Next 1H:', 'ANALYZING...'),
            ('Next 4H:', 'ANALYZING...'),
            ('Next 24H:', 'ANALYZING...'),
            ('Confidence:', '0%'),
            ('Signal:', 'HOLD')
        ]

        for label, value in prediction_data:
            row = tk.Frame(prediction_frame, bg=self.colors['bg_secondary'])
            row.pack(fill=tk.X, padx=5, pady=2)

            ttk.Label(row, text=label, style='Dark.TLabel').pack(side=tk.LEFT)
            value_label = ttk.Label(row, text=value, style='Dark.TLabel')
            value_label.pack(side=tk.RIGHT)
            self.prediction_labels[label] = value_label

        # Technical indicators
        indicators_frame = tk.LabelFrame(parent, text="TECHNICAL INDICATORS", bg=self.colors['bg_secondary'],
                                       fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        indicators_frame.pack(fill=tk.X, padx=5, pady=5)

        self.indicator_labels = {}
        indicator_data = [
            ('RSI (14):', '50.0'),
            ('MACD:', '0.0'),
            ('MA (20):', '$0.00'),
            ('MA (50):', '$0.00'),
            ('Volatility:', '0.0%')
        ]

        for label, value in indicator_data:
            row = tk.Frame(indicators_frame, bg=self.colors['bg_secondary'])
            row.pack(fill=tk.X, padx=5, pady=2)

            ttk.Label(row, text=label, style='Dark.TLabel').pack(side=tk.LEFT)
            value_label = ttk.Label(row, text=value, style='Dark.TLabel')
            value_label.pack(side=tk.RIGHT)
            self.indicator_labels[label] = value_label

        # Risk management
        risk_frame = tk.LabelFrame(parent, text="RISK MANAGEMENT", bg=self.colors['bg_secondary'],
                                 fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        risk_frame.pack(fill=tk.X, padx=5, pady=5)

        # Stop loss and take profit
        ttk.Label(risk_frame, text="Stop Loss:", style='Dark.TLabel').pack(anchor=tk.W, padx=5)
        self.stop_loss_var = tk.StringVar(value="0.00")
        stop_entry = tk.Entry(risk_frame, textvariable=self.stop_loss_var, bg=self.colors['bg_primary'],
                             fg=self.colors['text_primary'], font=('Consolas', 10))
        stop_entry.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(risk_frame, text="Take Profit:", style='Dark.TLabel').pack(anchor=tk.W, padx=5)
        self.take_profit_var = tk.StringVar(value="0.00")
        profit_entry = tk.Entry(risk_frame, textvariable=self.take_profit_var, bg=self.colors['bg_primary'],
                               fg=self.colors['text_primary'], font=('Consolas', 10))
        profit_entry.pack(fill=tk.X, padx=5, pady=2)

        # News and alerts
        news_frame = tk.LabelFrame(parent, text="MARKET NEWS & ALERTS", bg=self.colors['bg_secondary'],
                                 fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        news_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.news_text = scrolledtext.ScrolledText(news_frame, height=8, width=40,
                                                  bg=self.colors['bg_primary'],
                                                  fg=self.colors['text_primary'],
                                                  font=('Consolas', 9),
                                                  insertbackground=self.colors['text_primary'])
        self.news_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Initialize with sample news
        self.add_news("🚀 TRADVIO Terminal Initialized")
        self.add_news("📊 Real-time data streaming enabled")
        self.add_news("🤖 AI prediction engine ready")

    def update_clock(self):
        """Update real-time clock"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        self.clock_label.config(text=current_time)
        self.root.after(1000, self.update_clock)

    def select_coin(self, coin_id):
        """Select cryptocurrency for analysis"""
        self.selected_coin = coin_id
        coin_info = self.COINS[coin_id]

        # Update chart title
        self.chart_title.config(text=f"{coin_id}/USD - {coin_info['name'].upper()}")

        # Update button colors
        for cid, data in self.market_data.items():
            if cid == coin_id:
                data['button'].config(bg=self.colors['accent_blue'])
            else:
                data['button'].config(bg=self.COINS[cid]['color'])

        # Update position info
        self.position_labels['Asset:'].config(text=coin_id)

        self.log_message(f"📈 Selected {coin_info['name']} ({coin_id})")
        self.update_chart()

    def start_real_time_updates(self):
        """Start real-time data updates"""
        def update_loop():
            while True:
                try:
                    self.update_market_data()
                    time.sleep(30)  # Update every 30 seconds
                except Exception as e:
                    self.log_message(f"❌ Real-time update error: {str(e)}")
                    time.sleep(60)

        thread = threading.Thread(target=update_loop, daemon=True)
        thread.start()

    def update_market_data(self):
        """Update real-time market data"""
        try:
            for coin_id, coin_info in self.COINS.items():
                ticker = yf.Ticker(coin_info['symbol'])
                data = ticker.history(period="2d", interval="1h")

                if len(data) >= 2:
                    current_price = data['Close'].iloc[-1]
                    prev_price = data['Close'].iloc[-2]
                    change_pct = ((current_price - prev_price) / prev_price) * 100

                    # Update UI in main thread
                    self.root.after(0, self.update_coin_display, coin_id, current_price, change_pct)

                    # Store data
                    self.real_time_data[coin_id] = {
                        'price': current_price,
                        'change': change_pct,
                        'data': data
                    }

        except Exception as e:
            self.log_message(f"❌ Market data error: {str(e)}")

    def update_coin_display(self, coin_id, price, change_pct):
        """Update coin display in UI"""
        if coin_id in self.market_data:
            # Update price
            self.market_data[coin_id]['price_label'].config(text=f"${price:,.2f}")

            # Update change with color
            change_text = f"{change_pct:+.2f}%"
            change_color = self.colors['accent_green'] if change_pct >= 0 else self.colors['accent_red']

            # Update change label style
            style = ttk.Style()
            style.configure(f'{coin_id}.Change.TLabel',
                          background=self.colors['bg_secondary'],
                          foreground=change_color,
                          font=('Consolas', 10, 'bold'))

            self.market_data[coin_id]['change_label'].config(text=change_text,
                                                           style=f'{coin_id}.Change.TLabel')

            # Update main chart price if this is selected coin
            if coin_id == self.selected_coin:
                self.chart_price.config(text=f"${price:,.2f}")

    def update_chart(self):
        """Update main chart with current data"""
        try:
            coin_info = self.COINS[self.selected_coin]

            # Get data
            if self.selected_coin in self.real_time_data:
                data = self.real_time_data[self.selected_coin]['data']
            else:
                ticker = yf.Ticker(coin_info['symbol'])
                data = ticker.history(period="30d", interval="1h")

            if len(data) == 0:
                return

            # Clear axes
            self.ax_price.clear()
            self.ax_volume.clear()
            self.ax_indicators.clear()

            # Plot price chart
            self.ax_price.plot(data.index, data['Close'], color=coin_info['color'], linewidth=2, label='Price')
            self.ax_price.fill_between(data.index, data['Close'], alpha=0.3, color=coin_info['color'])

            # Add moving averages
            if len(data) >= 20:
                ma20 = data['Close'].rolling(20).mean()
                self.ax_price.plot(data.index, ma20, color=self.colors['accent_blue'],
                                 linewidth=1, alpha=0.8, label='MA20')

            if len(data) >= 50:
                ma50 = data['Close'].rolling(50).mean()
                self.ax_price.plot(data.index, ma50, color=self.colors['accent_yellow'],
                                 linewidth=1, alpha=0.8, label='MA50')

            # Style price chart
            self.ax_price.set_title(f'{self.selected_coin}/USD Price Chart',
                                  color=self.colors['text_primary'], fontsize=12, fontweight='bold')
            self.ax_price.legend(loc='upper left')
            self.ax_price.grid(True, alpha=0.3)

            # Plot volume
            colors = [self.colors['accent_green'] if close >= open_price else self.colors['accent_red']
                     for close, open_price in zip(data['Close'], data['Open'])]
            self.ax_volume.bar(data.index, data['Volume'], color=colors, alpha=0.7)
            self.ax_volume.set_title('Volume', color=self.colors['text_primary'], fontsize=10)
            self.ax_volume.grid(True, alpha=0.3)

            # Plot RSI
            if len(data) >= 14:
                from trading_functions import calculate_rsi
                rsi = calculate_rsi(data['Close'], 14)
                self.ax_indicators.plot(data.index[-len(rsi):], rsi, color=self.colors['accent_blue'], linewidth=2)
                self.ax_indicators.axhline(y=70, color=self.colors['accent_red'], linestyle='--', alpha=0.7)
                self.ax_indicators.axhline(y=30, color=self.colors['accent_green'], linestyle='--', alpha=0.7)
                self.ax_indicators.set_title('RSI (14)', color=self.colors['text_primary'], fontsize=10)
                self.ax_indicators.set_ylim(0, 100)
                self.ax_indicators.grid(True, alpha=0.3)

            # Style all axes
            for ax in [self.ax_price, self.ax_volume, self.ax_indicators]:
                ax.set_facecolor(self.colors['bg_primary'])
                ax.tick_params(colors=self.colors['text_secondary'])
                for spine in ax.spines.values():
                    spine.set_color(self.colors['border'])

            # Format x-axis
            self.ax_indicators.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d %H:%M'))
            self.ax_indicators.xaxis.set_major_locator(mdates.HourLocator(interval=6))

            self.canvas.draw()

        except Exception as e:
            self.log_message(f"❌ Chart update error: {str(e)}")

    def start_analysis(self):
        """Start AI analysis for selected coin"""
        if self.is_training:
            messagebox.showwarning("Warning", "Analysis already in progress!")
            return

        self.is_training = True
        self.predict_btn.config(text="🔄 ANALYZING...", state='disabled')

        # Update prediction labels
        for label in self.prediction_labels.values():
            label.config(text="ANALYZING...")

        thread = threading.Thread(target=self.run_analysis, daemon=True)
        thread.start()

    def run_analysis(self):
        """Run AI analysis in background"""
        try:
            coin_info = self.COINS[self.selected_coin]
            self.log_message(f"🤖 Starting AI analysis for {coin_info['name']}")

            # Get data
            ticker = yf.Ticker(coin_info['symbol'])
            data = ticker.history(period="2y")

            if len(data) < self.window_var.get():
                self.log_message("❌ Insufficient data for analysis")
                return

            # Import functions
            from trading_functions import preprocess_data, train_model, make_predictions, calculate_rmse, generate_trading_signal

            # Preprocessing
            X, y, scaler = preprocess_data(data, self.window_var.get())
            if len(X) == 0:
                self.log_message("❌ Preprocessing failed")
                return

            # Training
            self.log_message("🧠 Training AI model...")
            model, X_test, y_test, losses = train_model(X, y, self.epochs_var.get())
            if model is None:
                self.log_message("❌ Model training failed")
                return

            # Predictions
            self.log_message("🔮 Generating predictions...")
            predictions = make_predictions(model, X_test, scaler)
            if len(predictions) == 0:
                self.log_message("❌ Prediction generation failed")
                return

            # Calculate metrics
            y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1))
            rmse = calculate_rmse(y_test_actual, predictions)

            # Generate signals
            current_price = data['Close'].iloc[-1]
            predicted_price = predictions[-1][0] if len(predictions) > 0 else current_price

            # Calculate confidence based on RMSE
            confidence = max(0, min(100, 100 - (rmse / current_price * 100)))

            # Generate trading signal
            signal, signal_color = generate_trading_signal(current_price, predicted_price, confidence)

            # Update UI
            self.root.after(0, self.update_predictions, {
                'Next 1H:': f"${predicted_price:,.2f}",
                'Next 4H:': f"${predicted_price * 1.001:,.2f}",
                'Next 24H:': f"${predicted_price * 1.002:,.2f}",
                'Confidence:': f"{confidence:.1f}%",
                'Signal:': signal
            }, signal_color, rmse)

            self.log_message(f"✅ Analysis complete - RMSE: ${rmse:.2f}")
            self.log_message(f"🎯 Signal: {signal} (Confidence: {confidence:.1f}%)")

            # Store model
            self.models[self.selected_coin] = {
                'model': model,
                'scaler': scaler,
                'rmse': rmse,
                'confidence': confidence
            }

        except Exception as e:
            self.log_message(f"❌ Analysis error: {str(e)}")
        finally:
            self.root.after(0, self.finish_analysis)

    def update_predictions(self, predictions, signal_color, rmse):
        """Update prediction display"""
        for label, value in predictions.items():
            if label == 'Signal:':
                # Update signal with color
                style = ttk.Style()
                style.configure('Signal.TLabel',
                              background=self.colors['bg_secondary'],
                              foreground=signal_color,
                              font=('Consolas', 10, 'bold'))
                self.prediction_labels[label].config(text=value, style='Signal.TLabel')
            else:
                self.prediction_labels[label].config(text=value)

        # Update technical indicators
        if self.selected_coin in self.real_time_data:
            data = self.real_time_data[self.selected_coin]['data']
            self.update_technical_indicators(data)

    def update_technical_indicators(self, data):
        """Update technical indicators display"""
        try:
            from trading_functions import calculate_rsi, calculate_macd

            if len(data) >= 14:
                # RSI
                rsi = calculate_rsi(data['Close'], 14)
                current_rsi = rsi.iloc[-1] if len(rsi) > 0 else 50

                # MACD
                macd, signal_line, histogram = calculate_macd(data['Close'])
                current_macd = macd.iloc[-1] if len(macd) > 0 else 0

                # Moving averages
                ma20 = data['Close'].rolling(20).mean().iloc[-1] if len(data) >= 20 else 0
                ma50 = data['Close'].rolling(50).mean().iloc[-1] if len(data) >= 50 else 0

                # Volatility
                volatility = data['Close'].pct_change().std() * 100

                # Update labels
                self.indicator_labels['RSI (14):'].config(text=f"{current_rsi:.1f}")
                self.indicator_labels['MACD:'].config(text=f"{current_macd:.2f}")
                self.indicator_labels['MA (20):'].config(text=f"${ma20:.2f}")
                self.indicator_labels['MA (50):'].config(text=f"${ma50:.2f}")
                self.indicator_labels['Volatility:'].config(text=f"{volatility:.1f}%")

        except Exception as e:
            self.log_message(f"❌ Indicator update error: {str(e)}")

    def finish_analysis(self):
        """Finish analysis and reset UI"""
        self.is_training = False
        self.predict_btn.config(text="🚀 ANALYZE", state='normal')

    def toggle_streaming(self):
        """Toggle live streaming"""
        self.is_streaming = not self.is_streaming

        if self.is_streaming:
            self.stream_btn.config(text="⏹️ STOP STREAM", bg=self.colors['accent_red'])
            self.status_label.config(text="● STREAMING")
            self.log_message("📡 Live streaming started")
            self.start_streaming()
        else:
            self.stream_btn.config(text="📡 LIVE STREAM", bg=self.colors['accent_green'])
            self.status_label.config(text="● LIVE")
            self.log_message("⏹️ Live streaming stopped")

    def start_streaming(self):
        """Start live data streaming"""
        def stream_loop():
            while self.is_streaming:
                try:
                    self.update_market_data()
                    if self.selected_coin in self.real_time_data:
                        self.root.after(0, self.update_chart)
                    time.sleep(10)  # Update every 10 seconds when streaming
                except Exception as e:
                    self.log_message(f"❌ Streaming error: {str(e)}")
                    time.sleep(30)

        thread = threading.Thread(target=stream_loop, daemon=True)
        thread.start()

    def log_message(self, message):
        """Add message to terminal log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)

    def add_news(self, message):
        """Add news/alert message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.news_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.news_text.see(tk.END)


def main():
    """Main function"""
    root = tk.Tk()
    app = TradingTerminalApp(root)

    # Handle window close
    def on_closing():
        app.is_streaming = False
        app.is_training = False
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
