# 🧠 Penjelasan Implementasi LSTM Sederhana

## 📚 Apa itu LSTM?

**LSTM (Long Short-Term Memory)** adalah jenis Recurrent Neural Network (RNN) yang dirancang untuk mengatasi masalah vanishing gradient pada RNN tradisional. LSTM sangat efektif untuk data sequential seperti time series.

## 🔧 Implementasi LSTM dari Scratch

Aplikasi ini menggunakan implementasi LSTM sederhana dengan numpy, tanpa framework seperti TensorFlow/PyTorch. Ini membantu Anda memahami cara kerja LSTM secara detail.

### Komponen Utama LSTM:

#### 1. **Gates (Gerbang)**
LSTM memiliki 3 gerbang utama:

- **Forget Gate (f_t)**: Menentukan informasi mana yang akan "dilupakan"
- **Input Gate (i_t)**: Menentukan informasi baru mana yang akan disimpan
- **Output Gate (o_t)**: Menentukan bagian mana dari cell state yang akan dioutput

#### 2. **Cell State (C_t)**
Menyimpan informasi jangka panjang yang mengalir melalui network.

#### 3. **Hidden State (h_t)**
Output dari LSTM cell pada setiap time step.

### Formula Matematika:

```
f_t = σ(W_f · [h_{t-1}, x_t] + b_f)    # Forget gate
i_t = σ(W_i · [h_{t-1}, x_t] + b_i)    # Input gate  
C̃_t = tanh(W_C · [h_{t-1}, x_t] + b_C) # Candidate values
C_t = f_t * C_{t-1} + i_t * C̃_t        # Cell state
o_t = σ(W_o · [h_{t-1}, x_t] + b_o)    # Output gate
h_t = o_t * tanh(C_t)                   # Hidden state
```

Dimana:
- σ = sigmoid function
- W = weight matrices
- b = bias vectors
- * = element-wise multiplication

## 💻 Implementasi dalam Kode

### Class SimpleLSTM:

```python
class SimpleLSTM:
    def __init__(self, input_size=1, hidden_size=50, output_size=1):
        # Inisialisasi weights untuk setiap gate
        self.Wf = np.random.randn(hidden_size, input_size + hidden_size) * 0.1
        self.Wi = np.random.randn(hidden_size, input_size + hidden_size) * 0.1  
        self.Wo = np.random.randn(hidden_size, input_size + hidden_size) * 0.1
        self.Wc = np.random.randn(hidden_size, input_size + hidden_size) * 0.1
```

### Forward Pass:

```python
def forward(self, X):
    for t in range(seq_len):
        # Concatenate input dan hidden state
        concat = np.vstack([x_t, h])
        
        # Hitung gates
        f_t = self.sigmoid(np.dot(self.Wf, concat) + self.bf)
        i_t = self.sigmoid(np.dot(self.Wi, concat) + self.bi)
        o_t = self.sigmoid(np.dot(self.Wo, concat) + self.bo)
        c_tilde = self.tanh(np.dot(self.Wc, concat) + self.bc)
        
        # Update states
        c = f_t * c + i_t * c_tilde
        h = o_t * self.tanh(c)
```

## 🎯 Mengapa LSTM untuk Cryptocurrency?

1. **Sequential Data**: Harga crypto adalah data time series
2. **Long-term Dependencies**: LSTM dapat "mengingat" pola jangka panjang
3. **Volatility Handling**: Dapat menangani perubahan harga yang drastis
4. **Pattern Recognition**: Mengenali pola recurring dalam pergerakan harga

## ⚙️ Optimisasi untuk Core i3

### Parameter yang Dioptimalkan:

- **Hidden Size**: 20 (vs 50-100 pada implementasi standar)
- **Learning Rate**: 0.001 (konservatif untuk stabilitas)
- **Batch Size**: 32 (optimal untuk 16GB RAM)
- **Epochs**: 50 (balance antara akurasi dan waktu)

### Teknik Optimisasi:

1. **Gradient Clipping**: Mencegah exploding gradients
2. **Simplified Backprop**: Implementasi backpropagation yang disederhanakan
3. **Memory Management**: Efficient numpy operations
4. **Progress Tracking**: Real-time monitoring training progress

## 📊 Preprocessing Data

### 1. Normalisasi:
```python
scaler = MinMaxScaler(feature_range=(0, 1))
scaled_data = scaler.fit_transform(close_prices)
```

### 2. Windowing:
```python
# Membuat sequences 60 hari untuk prediksi 1 hari
for i in range(60, len(scaled_data)):
    X.append(scaled_data[i-60:i, 0])
    y.append(scaled_data[i, 0])
```

## 🎯 Trading Signals

### Logic Sinyal:
- **BUY**: Prediksi naik > 2% dari harga terakhir
- **SELL**: Prediksi turun > 2% dari harga terakhir  
- **HOLD**: Perubahan < 2%

```python
def generate_signal(actual_prices, predicted_prices):
    last_actual = actual_prices[-1]
    last_predicted = predicted_prices[-1][0]
    
    if last_predicted > last_actual * 1.02:
        return "🟢 BUY"
    elif last_predicted < last_actual * 0.98:
        return "🔴 SELL"
    else:
        return "🟡 HOLD"
```

## 📈 Evaluasi Model

### RMSE (Root Mean Square Error):
```python
def calculate_rmse(actual, predicted):
    return np.sqrt(mean_squared_error(actual, predicted))
```

RMSE mengukur rata-rata kesalahan prediksi dalam satuan yang sama dengan data asli (USD).

## 🚀 Tips Penggunaan

1. **Start Small**: Mulai dengan 1 koin untuk testing
2. **Monitor Performance**: Perhatikan penggunaan RAM dan CPU
3. **Adjust Parameters**: Sesuaikan epochs dan batch size sesuai kebutuhan
4. **Understand Limitations**: Model sederhana ini untuk pembelajaran, bukan trading nyata

## ⚠️ Limitasi

1. **Simplified Architecture**: Tidak secanggih implementasi framework
2. **Basic Backpropagation**: Gradient descent yang disederhanakan
3. **No Advanced Features**: Tidak ada attention mechanism, regularization lanjutan
4. **Educational Purpose**: Fokus pada pemahaman konsep, bukan performa maksimal

## 🎓 Pembelajaran Selanjutnya

Setelah memahami implementasi ini, Anda bisa:

1. **Improve Architecture**: Tambah layer, attention mechanism
2. **Advanced Optimization**: Implement Adam optimizer, learning rate scheduling
3. **Feature Engineering**: Tambah technical indicators
4. **Ensemble Methods**: Kombinasi multiple models
5. **Real-time Trading**: Integrasi dengan exchange APIs

Selamat belajar! 🚀📈
