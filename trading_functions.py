import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error

class SimplePredictor:
    """Model prediksi sederhana dengan technical indicators"""
    def __init__(self, window_size=10):
        self.window_size = window_size
        self.weights = None
        self.bias = 0
        self.learning_rate = 0.001
        
    def moving_average(self, data, window):
        """Hitung moving average"""
        if len(data) < window:
            return np.mean(data)
        return np.mean(data[-window:])
    
    def create_features(self, sequence):
        """Buat features dari sequence"""
        features = []
        
        # Moving averages dengan window berbeda
        for window in [3, 5, 10, 20]:
            if len(sequence) >= window:
                ma = self.moving_average(sequence, window)
                features.append(ma)
            else:
                features.append(sequence[-1] if len(sequence) > 0 else 0)
        
        # Trend features
        if len(sequence) >= 2:
            # Short term trend (last 3 days)
            short_trend = np.mean(np.diff(sequence[-3:])) if len(sequence) >= 3 else 0
            features.append(short_trend)
            
            # Long term trend (last 10 days)  
            long_trend = np.mean(np.diff(sequence[-10:])) if len(sequence) >= 10 else 0
            features.append(long_trend)
        else:
            features.extend([0, 0])
            
        # Volatility (standard deviation)
        if len(sequence) >= 5:
            volatility = np.std(sequence[-5:])
            features.append(volatility)
        else:
            features.append(0)
            
        # Recent price
        features.append(sequence[-1] if len(sequence) > 0 else 0)
        
        return np.array(features)
    
    def fit(self, X, y, epochs=100):
        """Training model dengan gradient descent"""
        n_samples, n_features = X.shape[0], 8  # 8 features yang kita buat
        
        # Initialize weights
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0
        
        losses = []
        
        # Training loop
        for epoch in range(epochs):
            epoch_loss = 0
            
            for i in range(n_samples):
                # Buat features dari sequence
                features = self.create_features(X[i].flatten())
                
                # Forward pass
                prediction = np.dot(features, self.weights) + self.bias
                
                # Calculate loss
                loss = (prediction - y[i]) ** 2
                epoch_loss += loss
                
                # Backward pass (gradient descent)
                error = prediction - y[i]
                
                # Update weights
                self.weights -= self.learning_rate * error * features
                self.bias -= self.learning_rate * error
            
            avg_loss = epoch_loss / n_samples
            losses.append(avg_loss)
            
            # Early stopping jika loss sudah konvergen
            if len(losses) > 10 and abs(losses[-1] - losses[-10]) < 1e-6:
                break
                
        return losses
    
    def predict(self, X):
        """Prediksi menggunakan model yang sudah ditraining"""
        if self.weights is None:
            raise ValueError("Model belum ditraining!")
            
        predictions = []
        
        for i in range(len(X)):
            features = self.create_features(X[i].flatten())
            prediction = np.dot(features, self.weights) + self.bias
            predictions.append(prediction)
            
        return np.array(predictions)


def preprocess_data(data, sequence_length=60):
    """Preprocessing data"""
    try:
        close_prices = data['Close'].values.reshape(-1, 1)
        
        # Normalisasi
        scaler = MinMaxScaler(feature_range=(0, 1))
        scaled_data = scaler.fit_transform(close_prices)
        
        # Windowing
        X, y = [], []
        for i in range(sequence_length, len(scaled_data)):
            X.append(scaled_data[i-sequence_length:i, 0])
            y.append(scaled_data[i, 0])
        
        X, y = np.array(X), np.array(y)
        X = np.reshape(X, (X.shape[0], X.shape[1], 1))
        
        return X, y, scaler
    except Exception as e:
        print(f"❌ Error preprocessing: {str(e)}")
        return np.array([]), np.array([]), None

def train_model(X, y, epochs):
    """Training model"""
    try:
        # Split data
        train_size = int(len(X) * 0.8)
        X_train, X_test = X[:train_size], X[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        # Create model
        model = SimplePredictor()
        
        # Training
        losses = model.fit(X_train, y_train, epochs)
        
        return model, X_test, y_test, losses
    except Exception as e:
        print(f"❌ Error training: {str(e)}")
        return None, None, None, []

def make_predictions(model, X_test, scaler):
    """Buat prediksi"""
    try:
        predictions = model.predict(X_test)
        predictions = predictions.reshape(-1, 1)
        predictions = scaler.inverse_transform(predictions)
        return predictions
    except Exception as e:
        print(f"❌ Error prediksi: {str(e)}")
        return np.array([]).reshape(-1, 1)

def calculate_rmse(actual, predicted):
    """Hitung RMSE"""
    return np.sqrt(mean_squared_error(actual, predicted))

def calculate_rsi(prices, period=14):
    """Calculate RSI indicator"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.dropna()

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD indicator"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    signal_line = macd.ewm(span=signal).mean()
    histogram = macd - signal_line
    return macd, signal_line, histogram

def generate_trading_signal(current_price, predicted_price, confidence):
    """Generate trading signal based on prediction"""
    price_change = (predicted_price - current_price) / current_price * 100
    
    if confidence < 50:
        return "🟡 HOLD", "#ffaa00"
    elif price_change > 3:
        return "🟢 STRONG BUY", "#00ff88"
    elif price_change > 1:
        return "🟢 BUY", "#00ff88"
    elif price_change < -3:
        return "🔴 STRONG SELL", "#ff4444"
    elif price_change < -1:
        return "🔴 SELL", "#ff4444"
    else:
        return "🟡 HOLD", "#ffaa00"
