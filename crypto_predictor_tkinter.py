import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import yfinance as yf
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
from datetime import datetime, timedelta
import threading
import warnings
warnings.filterwarnings('ignore')

class CryptoPredictorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 Cryptocurrency Price Predictor")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # Variabel untuk menyimpan data dan model
        self.models = {}
        self.data_cache = {}
        self.is_training = False
        
        # Konfigurasi cryptocurrency
        self.COINS = {
            'Bitcoin (BTC)': 'BTC-USD',
            'Ethereum (ETH)': 'ETH-USD', 
            'Solana (SOL)': 'SOL-USD',
            'Cardano (ADA)': 'ADA-USD',
            'XRP (XRP)': 'XRP-USD'
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup User Interface"""
        # Style configuration
        style = ttk.Style()
        style.theme_use('clam')
        
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        
        # Left panel - Controls
        self.setup_left_panel(main_frame)
        
        # Right panel - Results
        self.setup_right_panel(main_frame)
        
    def setup_left_panel(self, parent):
        """Setup panel kiri untuk kontrol"""
        left_frame = ttk.LabelFrame(parent, text="🎛️ Kontrol Aplikasi", padding="10")
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Cryptocurrency selection
        crypto_frame = ttk.LabelFrame(left_frame, text="📈 Pilih Cryptocurrency", padding="10")
        crypto_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.crypto_vars = {}
        for coin_name, symbol in self.COINS.items():
            var = tk.BooleanVar()
            self.crypto_vars[symbol] = var
            cb = ttk.Checkbutton(crypto_frame, text=coin_name, variable=var)
            cb.pack(anchor=tk.W, pady=2)
        
        # Parameters
        param_frame = ttk.LabelFrame(left_frame, text="⚙️ Parameter Model", padding="10")
        param_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Window Size
        ttk.Label(param_frame, text="Window Size (hari):").pack(anchor=tk.W)
        self.window_size_var = tk.IntVar(value=60)
        window_scale = ttk.Scale(param_frame, from_=30, to=90, variable=self.window_size_var, orient=tk.HORIZONTAL)
        window_scale.pack(fill=tk.X, pady=(0, 5))
        self.window_label = ttk.Label(param_frame, text="60")
        self.window_label.pack(anchor=tk.W)
        window_scale.configure(command=self.update_window_label)
        
        # Training Epochs
        ttk.Label(param_frame, text="Training Iterations:").pack(anchor=tk.W, pady=(10, 0))
        self.epochs_var = tk.IntVar(value=100)
        epochs_scale = ttk.Scale(param_frame, from_=50, to=200, variable=self.epochs_var, orient=tk.HORIZONTAL)
        epochs_scale.pack(fill=tk.X, pady=(0, 5))
        self.epochs_label = ttk.Label(param_frame, text="100")
        self.epochs_label.pack(anchor=tk.W)
        epochs_scale.configure(command=self.update_epochs_label)
        
        # Buttons
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.predict_button = ttk.Button(button_frame, text="🚀 Mulai Prediksi", command=self.start_prediction)
        self.predict_button.pack(fill=tk.X, pady=(0, 5))
        
        self.clear_button = ttk.Button(button_frame, text="🗑️ Clear Results", command=self.clear_results)
        self.clear_button.pack(fill=tk.X)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Siap untuk prediksi")
        ttk.Label(left_frame, textvariable=self.progress_var).pack(anchor=tk.W, pady=(10, 0))
        
        self.progress_bar = ttk.Progressbar(left_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # Log area
        log_frame = ttk.LabelFrame(left_frame, text="📝 Log", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=40)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def setup_right_panel(self, parent):
        """Setup panel kanan untuk hasil"""
        right_frame = ttk.LabelFrame(parent, text="📊 Hasil Prediksi", padding="10")
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # Results summary
        self.results_frame = ttk.Frame(right_frame)
        self.results_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Chart area
        self.chart_frame = ttk.Frame(right_frame)
        self.chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # Initial message
        self.show_welcome_message()
        
    def show_welcome_message(self):
        """Tampilkan pesan selamat datang"""
        welcome_label = ttk.Label(self.chart_frame, 
                                text="🚀 Selamat Datang di Cryptocurrency Predictor!\n\n"
                                     "📋 Cara Penggunaan:\n"
                                     "1. Pilih cryptocurrency di panel kiri\n"
                                     "2. Atur parameter model\n"
                                     "3. Klik 'Mulai Prediksi'\n"
                                     "4. Tunggu proses training selesai\n"
                                     "5. Lihat hasil prediksi dan grafik\n\n"
                                     "⚠️ Disclaimer: Hanya untuk tujuan edukasi",
                                font=('Arial', 12),
                                justify=tk.CENTER)
        welcome_label.pack(expand=True)
        
    def update_window_label(self, value):
        """Update label window size"""
        self.window_label.config(text=str(int(float(value))))
        
    def update_epochs_label(self, value):
        """Update label epochs"""
        self.epochs_label.config(text=str(int(float(value))))
        
    def log_message(self, message):
        """Tambahkan pesan ke log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def start_prediction(self):
        """Mulai proses prediksi"""
        if self.is_training:
            messagebox.showwarning("Warning", "Proses training sedang berjalan!")
            return
            
        # Cek apakah ada koin yang dipilih
        selected_coins = [symbol for symbol, var in self.crypto_vars.items() if var.get()]
        if not selected_coins:
            messagebox.showwarning("Warning", "Pilih minimal satu cryptocurrency!")
            return
            
        # Mulai training di thread terpisah
        self.is_training = True
        self.predict_button.config(state='disabled')
        self.progress_bar.start()
        
        thread = threading.Thread(target=self.run_prediction, args=(selected_coins,))
        thread.daemon = True
        thread.start()
        
    def run_prediction(self, selected_coins):
        """Jalankan prediksi untuk koin yang dipilih"""
        try:
            self.progress_var.set("Memulai prediksi...")
            self.log_message("🚀 Memulai proses prediksi")
            
            results = {}
            
            for i, symbol in enumerate(selected_coins):
                coin_name = [k for k, v in self.COINS.items() if v == symbol][0]
                
                self.progress_var.set(f"Processing {coin_name}...")
                self.log_message(f"📈 Memproses {coin_name}")
                
                # Download data
                data = self.get_crypto_data(symbol)
                if data is None:
                    continue
                    
                # Preprocessing
                X, y, scaler = self.preprocess_data(data, self.window_size_var.get())
                if len(X) == 0:
                    continue
                    
                # Training
                model, X_test, y_test, losses = self.train_model(X, y, self.epochs_var.get())
                if model is None:
                    continue
                    
                # Prediksi
                predictions = self.make_predictions(model, X_test, scaler)
                if len(predictions) == 0:
                    continue
                    
                # Simpan hasil
                y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1))
                rmse = self.calculate_rmse(y_test_actual, predictions)
                signal = self.generate_signal(data['Close'].values, predictions)
                
                results[symbol] = {
                    'coin_name': coin_name,
                    'data': data,
                    'actual': y_test_actual,
                    'predictions': predictions,
                    'rmse': rmse,
                    'signal': signal,
                    'losses': losses
                }
                
                self.log_message(f"✅ {coin_name} selesai - RMSE: ${rmse:.2f}")
                
            # Tampilkan hasil
            self.root.after(0, self.display_results, results)
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Error: {str(e)}"))
            self.log_message(f"❌ Error: {str(e)}")
        finally:
            self.root.after(0, self.finish_prediction)
            
    def finish_prediction(self):
        """Selesaikan proses prediksi"""
        self.is_training = False
        self.predict_button.config(state='normal')
        self.progress_bar.stop()
        self.progress_var.set("Prediksi selesai")
        self.log_message("🎉 Semua prediksi selesai!")
        
    def display_results(self, results):
        """Tampilkan hasil prediksi"""
        # Clear previous results
        for widget in self.results_frame.winfo_children():
            widget.destroy()
        for widget in self.chart_frame.winfo_children():
            widget.destroy()
            
        if not results:
            ttk.Label(self.chart_frame, text="Tidak ada hasil untuk ditampilkan").pack(expand=True)
            return
            
        # Create notebook for multiple coins
        notebook = ttk.Notebook(self.chart_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        for symbol, result in results.items():
            self.create_coin_tab(notebook, symbol, result)
            
    def create_coin_tab(self, notebook, symbol, result):
        """Buat tab untuk setiap koin"""
        # Create tab frame
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text=result['coin_name'])
        
        # Metrics frame
        metrics_frame = ttk.Frame(tab_frame)
        metrics_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Display metrics
        last_price = result['data']['Close'].iloc[-1]
        
        ttk.Label(metrics_frame, text=f"💰 Harga Terakhir: ${last_price:.2f}", 
                 font=('Arial', 12, 'bold')).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(metrics_frame, text=f"📊 RMSE: ${result['rmse']:.2f}", 
                 font=('Arial', 12)).pack(side=tk.LEFT, padx=(0, 20))
        
        # Signal dengan warna
        signal_color = 'green' if 'BUY' in result['signal'] else 'red' if 'SELL' in result['signal'] else 'orange'
        signal_label = ttk.Label(metrics_frame, text=f"🎯 Sinyal: {result['signal']}", 
                                font=('Arial', 12, 'bold'))
        signal_label.pack(side=tk.LEFT)
        
        # Chart frame
        chart_frame = ttk.Frame(tab_frame)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create matplotlib figure
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        fig.patch.set_facecolor('#f0f0f0')
        
        # Plot price prediction
        test_dates = result['data'].index[-len(result['actual']):]
        ax1.plot(test_dates, result['actual'], label='Harga Aktual', color='blue', linewidth=2)
        ax1.plot(test_dates, result['predictions'], label='Prediksi', color='red', linewidth=2, linestyle='--')
        ax1.set_title(f'Prediksi Harga {result["coin_name"]}', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Tanggal')
        ax1.set_ylabel('Harga (USD)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot training loss
        if result['losses']:
            ax2.plot(result['losses'], color='orange', linewidth=2)
            ax2.set_title('Training Loss', fontsize=12)
            ax2.set_xlabel('Iteration')
            ax2.set_ylabel('Loss')
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Embed plot in tkinter
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def clear_results(self):
        """Clear semua hasil"""
        for widget in self.results_frame.winfo_children():
            widget.destroy()
        for widget in self.chart_frame.winfo_children():
            widget.destroy()
            
        self.show_welcome_message()
        self.log_text.delete(1.0, tk.END)
        self.progress_var.set("Siap untuk prediksi")
        self.log_message("🗑️ Hasil dibersihkan")
        
    def get_crypto_data(self, symbol, period="2y"):
        """Ambil data cryptocurrency"""
        try:
            if symbol in self.data_cache:
                return self.data_cache[symbol]

            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            self.data_cache[symbol] = data
            return data
        except Exception as e:
            self.log_message(f"❌ Error mengambil data {symbol}: {str(e)}")
            return None

    def preprocess_data(self, data, sequence_length=60):
        """Preprocessing data"""
        try:
            close_prices = data['Close'].values.reshape(-1, 1)

            # Normalisasi
            scaler = MinMaxScaler(feature_range=(0, 1))
            scaled_data = scaler.fit_transform(close_prices)

            # Windowing
            X, y = [], []
            for i in range(sequence_length, len(scaled_data)):
                X.append(scaled_data[i-sequence_length:i, 0])
                y.append(scaled_data[i, 0])

            X, y = np.array(X), np.array(y)
            X = np.reshape(X, (X.shape[0], X.shape[1], 1))

            return X, y, scaler
        except Exception as e:
            self.log_message(f"❌ Error preprocessing: {str(e)}")
            return np.array([]), np.array([]), None

    def train_model(self, X, y, epochs):
        """Training model"""
        try:
            # Split data
            train_size = int(len(X) * 0.8)
            X_train, X_test = X[:train_size], X[train_size:]
            y_train, y_test = y[:train_size], y[train_size:]

            # Create model
            model = SimplePredictor()

            # Training
            losses = model.fit(X_train, y_train, epochs)

            return model, X_test, y_test, losses
        except Exception as e:
            self.log_message(f"❌ Error training: {str(e)}")
            return None, None, None, []

    def make_predictions(self, model, X_test, scaler):
        """Buat prediksi"""
        try:
            predictions = model.predict(X_test)
            predictions = predictions.reshape(-1, 1)
            predictions = scaler.inverse_transform(predictions)
            return predictions
        except Exception as e:
            self.log_message(f"❌ Error prediksi: {str(e)}")
            return np.array([]).reshape(-1, 1)

    def calculate_rmse(self, actual, predicted):
        """Hitung RMSE"""
        return np.sqrt(mean_squared_error(actual, predicted))

    def generate_signal(self, actual_prices, predicted_prices):
        """Generate trading signal"""
        if len(predicted_prices) < 2:
            return "🟡 HOLD"

        last_actual = actual_prices[-1]
        last_predicted = predicted_prices[-1][0]

        if last_predicted > last_actual * 1.02:
            return "🟢 BUY"
        elif last_predicted < last_actual * 0.98:
            return "🔴 SELL"
        else:
            return "🟡 HOLD"


class SimplePredictor:
    """Model prediksi sederhana"""
    def __init__(self, window_size=10):
        self.window_size = window_size
        self.weights = None
        self.bias = 0
        self.learning_rate = 0.001

    def moving_average(self, data, window):
        """Hitung moving average"""
        if len(data) < window:
            return np.mean(data)
        return np.mean(data[-window:])

    def create_features(self, sequence):
        """Buat features dari sequence"""
        features = []

        # Moving averages
        for window in [3, 5, 10, 20]:
            if len(sequence) >= window:
                ma = self.moving_average(sequence, window)
                features.append(ma)
            else:
                features.append(sequence[-1] if len(sequence) > 0 else 0)

        # Trend features
        if len(sequence) >= 2:
            short_trend = np.mean(np.diff(sequence[-3:])) if len(sequence) >= 3 else 0
            features.append(short_trend)

            long_trend = np.mean(np.diff(sequence[-10:])) if len(sequence) >= 10 else 0
            features.append(long_trend)
        else:
            features.extend([0, 0])

        # Volatility
        if len(sequence) >= 5:
            volatility = np.std(sequence[-5:])
            features.append(volatility)
        else:
            features.append(0)

        # Recent price
        features.append(sequence[-1] if len(sequence) > 0 else 0)

        return np.array(features)

    def fit(self, X, y, epochs=100):
        """Training model"""
        n_samples, n_features = X.shape[0], 8

        # Initialize weights
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0

        losses = []

        for epoch in range(epochs):
            epoch_loss = 0

            for i in range(n_samples):
                features = self.create_features(X[i].flatten())

                # Forward pass
                prediction = np.dot(features, self.weights) + self.bias

                # Calculate loss
                loss = (prediction - y[i]) ** 2
                epoch_loss += loss

                # Backward pass
                error = prediction - y[i]

                # Update weights
                self.weights -= self.learning_rate * error * features
                self.bias -= self.learning_rate * error

            avg_loss = epoch_loss / n_samples
            losses.append(avg_loss)

            # Early stopping
            if len(losses) > 10 and abs(losses[-1] - losses[-10]) < 1e-6:
                break

        return losses

    def predict(self, X):
        """Prediksi"""
        if self.weights is None:
            raise ValueError("Model belum ditraining!")

        predictions = []

        for i in range(len(X)):
            features = self.create_features(X[i].flatten())
            prediction = np.dot(features, self.weights) + self.bias
            predictions.append(prediction)

        return np.array(predictions)


def main():
    """Main function"""
    root = tk.Tk()
    app = CryptoPredictorApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
