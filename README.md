# 📊 TRADVIO - Professional Cryptocurrency Trading Terminal

Aplikasi trading terminal profesional yang terinspirasi dari Bloomberg Terminal dan platform trading institusional seperti Tradvio.com. Dirancang khusus untuk analisis cryptocurrency dengan AI prediction engine yang canggih, dioptimalkan untuk laptop Core i3 Gen 11 + 16GB RAM.

## 🎯 Fitur Utama

- **Bloomberg-style Interface**: 3-panel layout profesional dengan dark theme
- **Real-time Market Data**: Live streaming untuk BTC, ETH, SOL, ADA, dan XRP
- **AI Prediction Engine**: Machine learning dengan 8 technical indicators
- **Professional Charts**: Multi-timeframe dengan technical indicators
- **Trading Signals**: Advanced BUY/SELL/HOLD dengan confidence levels
- **Risk Management**: Stop loss, take profit, dan P&L tracking
- **Terminal Log**: Real-time logging dengan timestamp

## 📈 **Professional Trading Features**

- **Market Overview Panel**: Real-time prices dengan color coding
- **Main Chart Panel**: Professional charts dengan RSI, MACD, Volume
- **Analysis Panel**: AI predictions, technical indicators, news feed
- **Live Streaming**: Real-time data updates setiap 10-30 detik
- **Position Tracking**: Portfolio monitoring dan performance
- **Risk Controls**: Professional risk management tools

## 🛠️ Quick Start

### **Install Dependencies:**
```bash
pip install yfinance numpy pandas matplotlib scikit-learn
```

### **Jalankan TRADVIO Terminal:**

#### **Opsi 1: Double-click Batch File (TERMUDAH)**
```
run_app.bat
```

#### **Opsi 2: Command Line**
```bash
python tradvio_terminal.py
```

#### **Opsi 3: Full Python Path**
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe tradvio_terminal.py
```

## 📁 Struktur File

```
ML/
├── 📱 APLIKASI
│   ├── tradvio_terminal.py          # TRADVIO Trading Terminal
│   ├── trading_functions.py         # AI Model & Technical Indicators
│   ├── crypto_predictor.py          # Simple version (legacy)
│   ├── run_app.bat                  # Batch file launcher
│   └── requirements.txt             # Dependencies
│
├── 📚 DOKUMENTASI
│   ├── README.md                    # File ini
│   ├── TRADVIO_GUIDE.md            # Panduan TRADVIO Terminal
│   ├── QUICK_START.md              # Panduan cepat
│   ├── TKINTER_GUIDE.md            # Panduan teknis
│   ├── LSTM_Explanation.md         # Penjelasan teori ML
│   └── UPDATE_LOG.md               # Log perubahan
```

## 🚀 Cara Penggunaan

### **1. Install Dependencies**
```bash
pip install yfinance numpy pandas matplotlib scikit-learn
```

### **2. Jalankan Aplikasi**

#### **Termudah: Double-click Batch File**
```
run_app.bat
```

#### **Command Line:**
```bash
python crypto_predictor.py
```

### **3. Gunakan Aplikasi**
1. **Pilih cryptocurrency** (BTC, ETH, SOL, ADA, XRP)
2. **Atur parameter** model (window size, iterations)
3. **Klik "🚀 Mulai Prediksi"**
4. **Tunggu training** selesai (30-60 detik per koin)
5. **Analisis hasil** di tab masing-masing koin
6. **Lihat trading signals** dan grafik prediksi

## 🔧 Optimisasi Core i3

### **Parameter yang Dioptimalkan:**
- **Model**: SimplePredictor (bukan LSTM kompleks)
- **Features**: 8 technical indicators
- **Training**: 100 iterasi maksimal
- **Memory**: Efficient numpy operations
- **Threading**: Non-blocking UI (Tkinter)

### **Performance:**
- **Training Time**: 30-60 detik per koin
- **Memory Usage**: ~100MB
- **CPU Usage**: 30-50% (optimal untuk Core i3)

## 📊 Model Architecture

### **SimplePredictor Features:**
1. **Moving Average 3**: Short-term trend
2. **Moving Average 5**: Near-term trend
3. **Moving Average 10**: Medium-term trend
4. **Moving Average 20**: Long-term trend
5. **Short Trend**: 3-day change average
6. **Long Trend**: 10-day change average
7. **Volatility**: 5-day standard deviation
8. **Recent Price**: Latest closing price

### **Training Algorithm:**
- Gradient descent dengan feature engineering
- Early stopping untuk efisiensi
- Learning rate: 0.001 (stabil)

## 🎯 Trading Signals

- **🟢 BUY**: Prediksi naik > 2%
- **🔴 SELL**: Prediksi turun > 2%
- **🟡 HOLD**: Perubahan < 2%

## 📈 TRADVIO Terminal Interface

### **Professional Trading Layout:**
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ TRADVIO - Professional Cryptocurrency Trading Terminal    2024-01-15 14:30:25   │
├─────────────────┬─────────────────────────────────────────┬─────────────────────┤
│ MARKET OVERVIEW │                MAIN CHART               │ CURRENT POSITION    │
│ [BTC] $45,230   │                                         │ Asset: BTC          │
│ [ETH] $2,650    │        📈 Price Chart                   │ Quantity: 0.00      │
│ [SOL] $98.50    │                                         │ Avg Price: $0.00    │
│ [ADA] $0.45     │                                         │                     │
│ [XRP] $0.58     │                                         │ AI PREDICTIONS      │
│                 │        📊 Volume Chart                  │ Next 1H: $45,450   │
│ TRADING CONTROLS│                                         │ Next 4H: $45,680   │
│ Window: [60]    │        📉 RSI Indicator                 │ Next 24H: $46,120  │
│ Iterations:[100]│                                         │ Confidence: 78.5%   │
│                 │                                         │ Signal: 🟢 BUY     │
│ [🚀 ANALYZE]    │                                         │                     │
│ [📡 LIVE STREAM]│                                         │ TECHNICAL INDICATORS│
│                 │                                         │ RSI (14): 65.2      │
│ TERMINAL LOG    │                                         │ MACD: 125.8         │
│ [14:30] Started │                                         │ MA (20): $44,890    │
│ [14:31] BTC OK  │                                         │ MA (50): $43,250    │
│ [14:32] Analysis│                                         │ Volatility: 2.8%    │
└─────────────────┴─────────────────────────────────────────┴─────────────────────┘
```

## 🏆 Keunggulan TRADVIO Terminal

| Aspek | TRADVIO Professional |
|-------|---------------------|
| **Interface** | ⭐⭐⭐⭐⭐ Bloomberg-style |
| **Real-time Data** | ⭐⭐⭐⭐⭐ Live streaming |
| **AI Analysis** | ⭐⭐⭐⭐⭐ Advanced ML |
| **Technical Indicators** | ⭐⭐⭐⭐⭐ Professional |
| **Risk Management** | ⭐⭐⭐⭐⭐ Full suite |
| **Performance** | ⭐⭐⭐⭐⭐ Optimized |

## 📚 Dokumentasi Lengkap

- **[TRADVIO_GUIDE.md](TRADVIO_GUIDE.md)**: 📊 Panduan lengkap TRADVIO Terminal
- **[QUICK_START.md](QUICK_START.md)**: 🚀 Panduan cepat memulai
- **[TKINTER_GUIDE.md](TKINTER_GUIDE.md)**: Panduan teknis aplikasi desktop
- **[LSTM_Explanation.md](LSTM_Explanation.md)**: Penjelasan teori ML dan model
- **[UPDATE_LOG.md](UPDATE_LOG.md)**: Log perubahan dan perbaikan

## 🛠️ Troubleshooting

### **Common Issues:**
1. **"Python not found"**: Gunakan `run_app.bat`
2. **"ModuleNotFoundError"**: Install dependencies dengan `pip install -r requirements.txt`
3. **"No data"**: Check internet connection
4. **"App not responding"**: Tunggu training selesai

### **Performance Tips:**
- Start dengan 1-2 koin untuk testing
- Gunakan parameter default (Window: 60, Iterations: 100)
- Close aplikasi lain saat training untuk performa optimal

## ⚠️ Disclaimer

**PENTING**: Aplikasi ini hanya untuk tujuan edukasi dan pembelajaran machine learning.

- Cryptocurrency sangat volatile dan berisiko tinggi
- Prediksi tidak menjamin keuntungan trading
- Selalu lakukan riset sendiri sebelum berinvestasi
- Jangan investasikan uang yang tidak mampu Anda rugikan

## 🎓 Educational Value

### **Yang Dipelajari:**
- **Machine Learning**: Feature engineering, model training
- **Data Science**: Time series analysis, technical indicators
- **Programming**: Streamlit, Tkinter, threading
- **Finance**: Cryptocurrency analysis, trading signals

### **Next Steps:**
- Tambah technical indicators (RSI, MACD, Bollinger Bands)
- Implement ensemble methods
- Add backtesting functionality
- Create portfolio optimization

## 🚀 Getting Started

1. **Download/Clone** repository ini
2. **Install dependencies**: `pip install -r requirements.txt`
3. **Jalankan TRADVIO**: Double-click `run_app.bat` atau `python tradvio_terminal.py`
4. **Pilih cryptocurrency** dan klik "🚀 ANALYZE" untuk AI analysis
5. **Aktifkan live streaming** dengan "📡 LIVE STREAM"
6. **Monitor predictions** dan trading signals di panel kanan

**Selamat trading dengan TRADVIO Professional Terminal!** 📊🚀

---

**Developed with ❤️ for educational purposes**
