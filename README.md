# 🚀 Cryptocurrency Price Predictor

Aplikasi prediksi harga cryptocurrency dengan machine learning yang dioptimalkan untuk laptop Core i3 Gen 11 + 16GB RAM. Tersedia dalam **2 versi**: **Streamlit (Web)** dan **Tkinter (Desktop)**.

## 🎯 Fitur Utama

- **Multi-Cryptocurrency**: Prediksi untuk BTC, ETH, SOL, ADA, dan XRP
- **Smart Model**: SimplePredictor dengan 8 technical indicators
- **Real-time Data**: Data historis 2 tahun dari Yahoo Finance
- **Trading Signals**: Sinyal BUY/SELL/HOLD berdasarkan prediksi
- **Evaluasi Model**: RMSE untuk mengukur akurasi prediksi
- **Visualisasi**: Grafik interaktif harga aktual vs prediksi

## 🔄 Dua Versi Tersedia

### 🌐 **Streamlit Version (Web-based)**
- Modern web interface
- Browser-based application
- Easy sharing dan deployment
- Auto-refresh dan caching

### 🖥️ **Tkinter Version (Desktop)**
- Native desktop application
- Standalone executable
- Multi-tab interface
- Real-time progress tracking

## 🛠️ Quick Start

### **Streamlit Version:**
```bash
pip install streamlit yfinance numpy pandas matplotlib scikit-learn
streamlit run app.py
```

### **Tkinter Version:**
```bash
pip install yfinance numpy pandas matplotlib scikit-learn
python crypto_predictor_tkinter.py
```

**Atau double-click:** `run_tkinter_app.bat`

## 📁 Struktur File

```
ML/
├── 📱 APLIKASI
│   ├── app.py                          # Streamlit version
│   ├── crypto_predictor_tkinter.py     # Tkinter version
│   ├── run_tkinter_app.bat            # Batch file untuk Tkinter
│   └── requirements_simple.txt        # Dependencies
│
├── 📚 DOKUMENTASI
│   ├── README.md                      # File ini
│   ├── PANDUAN_PENGGUNAAN.md         # Panduan Streamlit
│   ├── TKINTER_GUIDE.md              # Panduan Tkinter
│   ├── STREAMLIT_VS_TKINTER.md       # Perbandingan versi
│   ├── ARSITEKTUR_APLIKASI.md        # Dokumentasi arsitektur
│   ├── LSTM_Explanation.md           # Penjelasan teori ML
│   └── UPDATE_LOG.md                 # Log perubahan
```

## 🚀 Cara Penggunaan

### **1. Pilih Versi**
- **Web/Sharing**: Gunakan Streamlit version
- **Desktop/Offline**: Gunakan Tkinter version

### **2. Install Dependencies**
```bash
pip install streamlit yfinance numpy pandas matplotlib scikit-learn
```

### **3. Jalankan Aplikasi**

#### **Streamlit:**
```bash
streamlit run app.py
# Buka browser: http://localhost:8501
```

#### **Tkinter:**
```bash
python crypto_predictor_tkinter.py
# Atau double-click: run_tkinter_app.bat
```

### **4. Gunakan Aplikasi**
1. Pilih cryptocurrency (BTC, ETH, SOL, ADA, XRP)
2. Atur parameter model (window size, iterations)
3. Klik "Mulai Prediksi"
4. Tunggu training selesai
5. Analisis hasil dan trading signals

## 🔧 Optimisasi Core i3

### **Parameter yang Dioptimalkan:**
- **Model**: SimplePredictor (bukan LSTM kompleks)
- **Features**: 8 technical indicators
- **Training**: 100 iterasi maksimal
- **Memory**: Efficient numpy operations
- **Threading**: Non-blocking UI (Tkinter)

### **Performance:**
- **Training Time**: 30-60 detik per koin
- **Memory Usage**: ~100MB
- **CPU Usage**: 30-50% (optimal untuk Core i3)

## 📊 Model Architecture

### **SimplePredictor Features:**
1. **Moving Average 3**: Short-term trend
2. **Moving Average 5**: Near-term trend
3. **Moving Average 10**: Medium-term trend
4. **Moving Average 20**: Long-term trend
5. **Short Trend**: 3-day change average
6. **Long Trend**: 10-day change average
7. **Volatility**: 5-day standard deviation
8. **Recent Price**: Latest closing price

### **Training Algorithm:**
- Gradient descent dengan feature engineering
- Early stopping untuk efisiensi
- Learning rate: 0.001 (stabil)

## 🎯 Trading Signals

- **🟢 BUY**: Prediksi naik > 2%
- **🔴 SELL**: Prediksi turun > 2%
- **🟡 HOLD**: Perubahan < 2%

## 📈 Screenshots

### **Streamlit Version:**
```
🚀 Cryptocurrency Price Predictor with LSTM
├── Sidebar: Pilih Cryptocurrency
├── Parameter: Window Size, Epochs, Batch Size
├── Main: Grafik dan Metrics
└── Expandable: Training History
```

### **Tkinter Version:**
```
┌─────────────────────────────────────────────────────────────┐
│ 🚀 Cryptocurrency Price Predictor                          │
├─────────────────┬───────────────────────────────────────────┤
│ 🎛️ Kontrol      │ 📊 Hasil Prediksi                        │
│ ☑️ Crypto       │ ┌─ BTC ─┬─ ETH ─┬─ SOL ─┐                │
│ ⚙️ Parameter    │ │ Chart │ Chart │ Chart │                │
│ 🚀 Predict      │ └───────┴───────┴───────┘                │
│ 📝 Log          │                                           │
└─────────────────┴───────────────────────────────────────────┘
```

## 🔍 Perbandingan Versi

| Aspek | Streamlit | Tkinter |
|-------|-----------|---------|
| **Interface** | Modern web UI | Native desktop |
| **Deployment** | Browser required | Standalone |
| **Performance** | Good | Excellent |
| **Sharing** | Easy | Local only |
| **Customization** | Limited | Full control |
| **Development** | Very fast | Moderate |

## 📚 Dokumentasi Lengkap

- **[PANDUAN_PENGGUNAAN.md](PANDUAN_PENGGUNAAN.md)**: Panduan lengkap Streamlit
- **[TKINTER_GUIDE.md](TKINTER_GUIDE.md)**: Panduan lengkap Tkinter
- **[STREAMLIT_VS_TKINTER.md](STREAMLIT_VS_TKINTER.md)**: Perbandingan detail
- **[ARSITEKTUR_APLIKASI.md](ARSITEKTUR_APLIKASI.md)**: Technical architecture
- **[LSTM_Explanation.md](LSTM_Explanation.md)**: Penjelasan teori ML
- **[UPDATE_LOG.md](UPDATE_LOG.md)**: Log perubahan dan perbaikan

## 🛠️ Troubleshooting

### **Common Issues:**
1. **"Python not found"**: Gunakan `run_tkinter_app.bat`
2. **"ModuleNotFoundError"**: Install dependencies
3. **"No data"**: Check internet connection
4. **"App not responding"**: Tunggu training selesai

### **Performance Tips:**
- Start dengan 1-2 koin untuk testing
- Gunakan parameter default
- Close aplikasi lain saat training

## ⚠️ Disclaimer

**PENTING**: Aplikasi ini hanya untuk tujuan edukasi dan pembelajaran machine learning.

- Cryptocurrency sangat volatile dan berisiko tinggi
- Prediksi tidak menjamin keuntungan trading
- Selalu lakukan riset sendiri sebelum berinvestasi
- Jangan investasikan uang yang tidak mampu Anda rugikan

## 🎓 Educational Value

### **Yang Dipelajari:**
- **Machine Learning**: Feature engineering, model training
- **Data Science**: Time series analysis, technical indicators
- **Programming**: Streamlit, Tkinter, threading
- **Finance**: Cryptocurrency analysis, trading signals

### **Next Steps:**
- Tambah technical indicators (RSI, MACD, Bollinger Bands)
- Implement ensemble methods
- Add backtesting functionality
- Create portfolio optimization

## 🚀 Getting Started

1. **Download/Clone** repository ini
2. **Pilih versi** yang sesuai kebutuhan
3. **Install dependencies** sesuai panduan
4. **Jalankan aplikasi** dan mulai eksperimen
5. **Baca dokumentasi** untuk pemahaman mendalam

**Selamat belajar machine learning dan cryptocurrency analysis!** 🎓📈

---

**Developed with ❤️ for educational purposes**
