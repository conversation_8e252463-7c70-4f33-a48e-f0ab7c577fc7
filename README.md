# 🚀 Cryptocurrency Price Predictor

Aplikasi desktop prediksi harga cryptocurrency dengan machine learning yang dioptimalkan untuk laptop Core i3 Gen 11 + 16GB RAM. Menggunakan **Tkinter** untuk interface desktop yang native dan responsif.

## 🎯 Fitur Utama

- **Multi-Cryptocurrency**: Prediksi untuk BTC, ETH, SOL, ADA, dan XRP
- **Smart Model**: SimplePredictor dengan 8 technical indicators
- **Real-time Data**: Data historis 2 tahun dari Yahoo Finance
- **Trading Signals**: Sinyal BUY/SELL/HOLD berdasarkan prediksi
- **Evaluasi Model**: RMSE untuk mengukur akurasi prediksi
- **Visualisasi**: Grafik interaktif harga aktual vs prediksi
- **Desktop Native**: Interface desktop yang cepat dan responsif

## 🖥️ **Desktop Application Features**

- **Native Desktop Interface**: Tidak perlu browser
- **Multi-tab Results**: Setiap cryptocurrency di tab terpisah
- **Real-time Progress**: Progress bar dan log real-time
- **Threading**: Non-blocking UI operations
- **Interactive Charts**: Matplotlib terintegrasi
- **Standalone**: Bisa jalan offline setelah download data

## 🛠️ Quick Start

### **Install Dependencies:**
```bash
pip install yfinance numpy pandas matplotlib scikit-learn
```

### **Jalankan Aplikasi:**

#### **Opsi 1: Double-click Batch File (TERMUDAH)**
```
run_app.bat
```

#### **Opsi 2: Command Line**
```bash
python crypto_predictor.py
```

#### **Opsi 3: Full Python Path**
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe crypto_predictor.py
```

## 📁 Struktur File

```
ML/
├── 📱 APLIKASI
│   ├── crypto_predictor.py           # Main aplikasi desktop
│   ├── run_app.bat                   # Batch file untuk menjalankan
│   └── requirements.txt              # Dependencies
│
├── 📚 DOKUMENTASI
│   ├── README.md                     # File ini
│   ├── TKINTER_GUIDE.md             # Panduan lengkap
│   ├── LSTM_Explanation.md          # Penjelasan teori ML
│   └── UPDATE_LOG.md                # Log perubahan
```

## 🚀 Cara Penggunaan

### **1. Install Dependencies**
```bash
pip install yfinance numpy pandas matplotlib scikit-learn
```

### **2. Jalankan Aplikasi**

#### **Termudah: Double-click Batch File**
```
run_app.bat
```

#### **Command Line:**
```bash
python crypto_predictor.py
```

### **3. Gunakan Aplikasi**
1. **Pilih cryptocurrency** (BTC, ETH, SOL, ADA, XRP)
2. **Atur parameter** model (window size, iterations)
3. **Klik "🚀 Mulai Prediksi"**
4. **Tunggu training** selesai (30-60 detik per koin)
5. **Analisis hasil** di tab masing-masing koin
6. **Lihat trading signals** dan grafik prediksi

## 🔧 Optimisasi Core i3

### **Parameter yang Dioptimalkan:**
- **Model**: SimplePredictor (bukan LSTM kompleks)
- **Features**: 8 technical indicators
- **Training**: 100 iterasi maksimal
- **Memory**: Efficient numpy operations
- **Threading**: Non-blocking UI (Tkinter)

### **Performance:**
- **Training Time**: 30-60 detik per koin
- **Memory Usage**: ~100MB
- **CPU Usage**: 30-50% (optimal untuk Core i3)

## 📊 Model Architecture

### **SimplePredictor Features:**
1. **Moving Average 3**: Short-term trend
2. **Moving Average 5**: Near-term trend
3. **Moving Average 10**: Medium-term trend
4. **Moving Average 20**: Long-term trend
5. **Short Trend**: 3-day change average
6. **Long Trend**: 10-day change average
7. **Volatility**: 5-day standard deviation
8. **Recent Price**: Latest closing price

### **Training Algorithm:**
- Gradient descent dengan feature engineering
- Early stopping untuk efisiensi
- Learning rate: 0.001 (stabil)

## 🎯 Trading Signals

- **🟢 BUY**: Prediksi naik > 2%
- **🔴 SELL**: Prediksi turun > 2%
- **🟡 HOLD**: Perubahan < 2%

## 📈 Interface Preview

### **Desktop Application Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ 🚀 Cryptocurrency Price Predictor                          │
├─────────────────┬───────────────────────────────────────────┤
│ 🎛️ Kontrol      │ 📊 Hasil Prediksi                        │
│                 │                                           │
│ ☑️ Bitcoin      │ ┌─ Bitcoin ─┬─ Ethereum ─┬─ Solana ─┐    │
│ ☑️ Ethereum     │ │ 💰 $45,230 │            │          │    │
│ ☐ Solana       │ │ 📊 $234    │            │          │    │
│ ☐ Cardano      │ │ 🟢 BUY     │            │          │    │
│ ☐ XRP          │ │            │            │          │    │
│                 │ │ [Chart]    │            │          │    │
│ Window: [60]    │ │            │            │          │    │
│ Epochs: [100]   │ └────────────┴────────────┴──────────┘    │
│                 │                                           │
│ 🚀 Mulai        │                                           │
│ 🗑️ Clear        │                                           │
│                 │                                           │
│ Progress: ████  │                                           │
│                 │                                           │
│ 📝 Log:         │                                           │
│ [12:34] Start   │                                           │
│ [12:35] BTC OK  │                                           │
└─────────────────┴───────────────────────────────────────────┘
```

## 🏆 Keunggulan Desktop Version

| Aspek | Desktop Tkinter |
|-------|-----------------|
| **Performance** | ⭐⭐⭐⭐⭐ Excellent |
| **Startup Time** | ⭐⭐⭐⭐⭐ 1-2 detik |
| **Memory Usage** | ⭐⭐⭐⭐⭐ ~100MB |
| **Responsiveness** | ⭐⭐⭐⭐⭐ Native |
| **Offline Usage** | ⭐⭐⭐⭐⭐ Full support |
| **Customization** | ⭐⭐⭐⭐⭐ Full control |

## 📚 Dokumentasi Lengkap

- **[QUICK_START.md](QUICK_START.md)**: 🚀 Panduan cepat memulai
- **[TKINTER_GUIDE.md](TKINTER_GUIDE.md)**: Panduan lengkap aplikasi desktop
- **[LSTM_Explanation.md](LSTM_Explanation.md)**: Penjelasan teori ML dan model
- **[UPDATE_LOG.md](UPDATE_LOG.md)**: Log perubahan dan perbaikan

## 🛠️ Troubleshooting

### **Common Issues:**
1. **"Python not found"**: Gunakan `run_app.bat`
2. **"ModuleNotFoundError"**: Install dependencies dengan `pip install -r requirements.txt`
3. **"No data"**: Check internet connection
4. **"App not responding"**: Tunggu training selesai

### **Performance Tips:**
- Start dengan 1-2 koin untuk testing
- Gunakan parameter default (Window: 60, Iterations: 100)
- Close aplikasi lain saat training untuk performa optimal

## ⚠️ Disclaimer

**PENTING**: Aplikasi ini hanya untuk tujuan edukasi dan pembelajaran machine learning.

- Cryptocurrency sangat volatile dan berisiko tinggi
- Prediksi tidak menjamin keuntungan trading
- Selalu lakukan riset sendiri sebelum berinvestasi
- Jangan investasikan uang yang tidak mampu Anda rugikan

## 🎓 Educational Value

### **Yang Dipelajari:**
- **Machine Learning**: Feature engineering, model training
- **Data Science**: Time series analysis, technical indicators
- **Programming**: Streamlit, Tkinter, threading
- **Finance**: Cryptocurrency analysis, trading signals

### **Next Steps:**
- Tambah technical indicators (RSI, MACD, Bollinger Bands)
- Implement ensemble methods
- Add backtesting functionality
- Create portfolio optimization

## 🚀 Getting Started

1. **Download/Clone** repository ini
2. **Install dependencies**: `pip install -r requirements.txt`
3. **Jalankan aplikasi**: Double-click `run_app.bat` atau `python crypto_predictor.py`
4. **Mulai eksperimen** dengan memilih cryptocurrency dan parameter
5. **Baca dokumentasi** untuk pemahaman mendalam

**Selamat belajar machine learning dan cryptocurrency analysis dengan aplikasi desktop!** 🎓📈

---

**Developed with ❤️ for educational purposes**
