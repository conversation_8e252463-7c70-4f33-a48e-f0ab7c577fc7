# 🚀 Quick Start Guide

## ⚡ Cara Tercepat Menjalankan Aplikasi

### **1. Double-click File Batch**
```
run_app.bat
```
**Selesai!** Aplikasi akan terbuka otomatis.

---

## 🔧 Jika Ada Masalah

### **Install Dependencies Dulu:**
```bash
pip install yfinance numpy pandas matplotlib scikit-learn
```

### **Atau Install dari Requirements:**
```bash
pip install -r requirements.txt
```

### **Lalu Jalankan Manual:**
```bash
python crypto_predictor.py
```

---

## 🎮 Cara Menggunakan

1. **✅ Centang cryptocurrency** yang ingin diprediksi
2. **⚙️ Atur parameter** (biarkan default untuk pertama kali)
3. **🚀 Klik "Mulai Prediksi"**
4. **⏳ Tunggu 30-60 detik** per koin
5. **📊 Lihat hasil** di tab masing-masing

---

## 💡 Tips Pertama Kali

- **Pilih 1 koin saja** (Bitcoin) untuk testing
- **Gunakan parameter default** (Window: 60, Iterations: 100)
- **Tunggu sampai selesai** jangan close aplikasi
- **Lihat log** di panel kiri untuk progress

---

## 🎯 Hasil yang Diharapkan

Setelah training selesai, Anda akan melihat:
- **💰 Harga terakhir** cryptocurrency
- **📊 RMSE** (akurasi model)
- **🎯 Trading signal** (BUY/SELL/HOLD)
- **📈 Grafik** prediksi vs harga aktual
- **📉 Training loss** (konvergensi model)

---

## ⚠️ Disclaimer

**Aplikasi ini hanya untuk edukasi!**
- Jangan gunakan untuk trading nyata
- Cryptocurrency sangat berisiko
- Selalu lakukan riset sendiri

---

**Selamat mencoba! 🎉**
