# 🏗️ Arsitektur Aplikasi Cryptocurrency Predictor

## 📁 Struktur File

```
ML/
├── app.py                    # Main aplikasi Streamlit
├── requirements_simple.txt   # Dependencies Python
├── PANDUAN_PENGGUNAAN.md    # Panduan lengkap penggunaan
├── LSTM_Explanation.md      # Penjelasan teori LSTM
└── ARSITEKTUR_APLIKASI.md   # Dokumentasi arsitektur (file ini)
```

## 🔧 Komponen Utama Aplikasi

### 1. **User Interface (Streamlit)**
```python
# Konfigurasi halaman
st.set_page_config(
    page_title="Crypto Price Predictor",
    page_icon="₿",
    layout="wide"
)

# Sidebar untuk kontrol
st.sidebar.header("Pilih Cryptocurrency")
selected_coins = {}
for coin_name, symbol in COINS.items():
    selected_coins[symbol] = st.sidebar.checkbox(coin_name)
```

**Fitur UI:**
- Sidebar dengan checkbox untuk pemilihan koin
- Parameter tuning (window size, epochs, batch size)
- Progress bar untuk training
- Grafik interaktif dengan matplotlib
- Metrics display (harga, RMSE, sinyal)

### 2. **Data Layer**
```python
@st.cache_data
def get_crypto_data(symbol, period="2y"):
    ticker = yf.Ticker(symbol)
    data = ticker.history(period=period)
    return data
```

**Fungsi:**
- Mengambil data dari Yahoo Finance API
- Caching untuk menghindari download berulang
- Error handling untuk koneksi gagal
- Support 5 cryptocurrency: BTC, ETH, SOL, ADA, XRP

### 3. **Preprocessing Layer**
```python
def preprocess_data(data, sequence_length=60):
    # Normalisasi dengan MinMaxScaler
    scaler = MinMaxScaler(feature_range=(0, 1))
    scaled_data = scaler.fit_transform(close_prices)
    
    # Windowing untuk sequences
    X, y = [], []
    for i in range(sequence_length, len(scaled_data)):
        X.append(scaled_data[i-sequence_length:i, 0])
        y.append(scaled_data[i, 0])
```

**Proses:**
- Normalisasi data ke range 0-1
- Windowing 60 hari untuk prediksi 1 hari
- Train-test split 80:20
- Reshape untuk input LSTM

### 4. **Model Layer (LSTM Implementation)**
```python
class SimpleLSTM:
    def __init__(self, input_size=1, hidden_size=20, output_size=1):
        # Weight initialization
        self.Wf = np.random.randn(hidden_size, input_size + hidden_size) * 0.1
        self.Wi = np.random.randn(hidden_size, input_size + hidden_size) * 0.1
        self.Wo = np.random.randn(hidden_size, input_size + hidden_size) * 0.1
        self.Wc = np.random.randn(hidden_size, input_size + hidden_size) * 0.1
```

**Arsitektur LSTM:**
- Input size: 1 (harga close)
- Hidden size: 20 (optimized untuk Core i3)
- Output size: 1 (prediksi harga)
- 3 Gates: Forget, Input, Output
- Cell state dan Hidden state

### 5. **Training Layer**
```python
def train_simple_model(X, y, sequence_length, epochs, batch_size):
    model = SimpleLSTM(input_size=1, hidden_size=20, output_size=1)
    
    for epoch in range(epochs):
        for i in range(0, len(X_train), batch_size):
            # Batch training
            batch_loss = model.train_step(seq, target)
```

**Optimisasi:**
- Batch size: 32 (optimal untuk 16GB RAM)
- Epochs: 50 (balance waktu vs akurasi)
- Learning rate: 0.001 (stabil)
- Progress tracking real-time

### 6. **Prediction & Signal Layer**
```python
def generate_signal(actual_prices, predicted_prices):
    if last_predicted > last_actual * 1.02:
        return "🟢 BUY"
    elif last_predicted < last_actual * 0.98:
        return "🔴 SELL"
    else:
        return "🟡 HOLD"
```

**Logic:**
- Threshold 2% untuk sinyal BUY/SELL
- RMSE calculation untuk evaluasi
- Inverse transform ke skala asli

## 🔄 Flow Aplikasi

### 1. **Initialization**
```
User opens app → Streamlit loads → UI renders → Sidebar ready
```

### 2. **Data Pipeline**
```
User selects coins → Download from Yahoo Finance → Cache data → Preprocess
```

### 3. **Training Pipeline**
```
Preprocess data → Initialize LSTM → Train model → Track progress → Save model
```

### 4. **Prediction Pipeline**
```
Load trained model → Make predictions → Generate signals → Display results
```

### 5. **Visualization Pipeline**
```
Prepare data → Create matplotlib plots → Display in Streamlit → Interactive features
```

## 🎯 Optimisasi Performa

### Memory Management:
- **Numpy arrays**: Efficient memory usage
- **Streamlit caching**: Avoid redundant downloads
- **Batch processing**: Control memory consumption
- **Garbage collection**: Automatic cleanup

### CPU Optimization:
- **Vectorized operations**: Numpy optimizations
- **Small model size**: 20 hidden units vs 50-100 standard
- **Efficient loops**: Minimized Python loops
- **Progress tracking**: Non-blocking UI updates

### User Experience:
- **Progress bars**: Real-time training feedback
- **Error handling**: Graceful failure recovery
- **Responsive UI**: Non-blocking operations
- **Clear messaging**: User-friendly notifications

## 🔒 Error Handling

### Data Layer Errors:
```python
try:
    ticker = yf.Ticker(symbol)
    data = ticker.history(period=period)
    return data
except Exception as e:
    st.error(f"Error mengambil data {symbol}: {str(e)}")
    return None
```

### Model Training Errors:
- Memory overflow protection
- Invalid parameter handling
- Training convergence monitoring
- Graceful degradation

### UI Error Handling:
- Input validation
- State management
- Session recovery
- User feedback

## 📊 Data Flow Diagram

```
Yahoo Finance API
        ↓
    Raw Price Data
        ↓
   Preprocessing
   (Normalization + Windowing)
        ↓
    Training Data
        ↓
    LSTM Model
        ↓
    Predictions
        ↓
   Signal Generation
        ↓
    UI Display
```

## 🔧 Konfigurasi Sistem

### Dependencies:
- **streamlit**: Web framework
- **yfinance**: Data source
- **numpy**: Numerical computing
- **pandas**: Data manipulation
- **matplotlib**: Visualization
- **scikit-learn**: Preprocessing

### System Requirements:
- **Python**: 3.13+
- **RAM**: 16GB (recommended)
- **CPU**: Intel Core i3 Gen 11+
- **Storage**: 100MB free space
- **Internet**: Required for data download

## 🚀 Deployment Options

### Local Development:
```bash
streamlit run app.py
```

### Production Deployment:
- **Streamlit Cloud**: Free hosting
- **Heroku**: Container deployment
- **Docker**: Containerized deployment
- **AWS/GCP**: Cloud deployment

## 🔮 Future Enhancements

### Model Improvements:
- Multi-layer LSTM
- Attention mechanisms
- Ensemble methods
- Advanced optimizers (Adam, RMSprop)

### Feature Additions:
- Technical indicators (RSI, MACD)
- Sentiment analysis
- News integration
- Portfolio optimization

### Performance Optimizations:
- GPU support (when available)
- Parallel processing
- Model compression
- Real-time streaming

### UI/UX Enhancements:
- Dark mode
- Mobile responsive
- Advanced charting
- Export functionality

## 📈 Monitoring & Analytics

### Performance Metrics:
- Training time per epoch
- Memory usage tracking
- Prediction accuracy (RMSE)
- User interaction analytics

### Logging:
- Error tracking
- Performance monitoring
- User behavior analysis
- System health checks

Arsitektur ini dirancang untuk pembelajaran dan eksperimen dengan machine learning pada data cryptocurrency, dengan fokus pada kemudahan pemahaman dan optimisasi untuk hardware terbatas. 🚀📊
