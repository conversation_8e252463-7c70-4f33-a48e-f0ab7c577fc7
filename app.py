import streamlit as st
import yfinance as yf
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Konfigurasi halaman Streamlit
st.set_page_config(
    page_title="Crypto Price Predictor",
    page_icon="₿",
    layout="wide"
)

# Judul aplikasi
st.title("🚀 Cryptocurrency Price Predictor with LSTM")
st.markdown("Prediksi harga cryptocurrency menggunakan model LSTM yang dioptimalkan untuk laptop Core i3")

# Sidebar untuk pemilihan koin
st.sidebar.header("Pilih Cryptocurrency")
st.sidebar.markdown("Centang koin yang ingin diprediksi:")

# Dictionary koin dengan simbol Yahoo Finance
COINS = {
    'Bitcoin (BTC)': 'BTC-USD',
    'Ethereum (ETH)': 'ETH-USD', 
    'Solana (SOL)': 'SOL-USD',
    'Cardano (ADA)': 'ADA-USD',
    'XRP (XRP)': 'XRP-USD'
}

# Checkbox untuk setiap koin
selected_coins = {}
for coin_name, symbol in COINS.items():
    selected_coins[symbol] = st.sidebar.checkbox(coin_name, value=False)

# Parameter model (dioptimalkan untuk Core i3)
st.sidebar.header("Parameter Model")
SEQUENCE_LENGTH = st.sidebar.slider("Window Size (hari)", 30, 90, 60)
EPOCHS = st.sidebar.slider("Training Epochs", 20, 100, 50)
BATCH_SIZE = st.sidebar.slider("Batch Size", 16, 64, 32)

@st.cache_data
def get_crypto_data(symbol, period="2y"):
    """
    Mengambil data historis cryptocurrency dari Yahoo Finance
    Cache digunakan untuk menghindari download berulang
    """
    try:
        ticker = yf.Ticker(symbol)
        data = ticker.history(period=period)
        return data
    except Exception as e:
        st.error(f"Error mengambil data {symbol}: {str(e)}")
        return None

def preprocess_data(data, sequence_length=60):
    """
    Preprocessing data:
    1. Normalisasi menggunakan MinMaxScaler
    2. Membuat sequences untuk LSTM (windowing)
    """
    # Ambil hanya kolom Close
    close_prices = data['Close'].values.reshape(-1, 1)
    
    # Normalisasi data ke range 0-1
    scaler = MinMaxScaler(feature_range=(0, 1))
    scaled_data = scaler.fit_transform(close_prices)
    
    # Membuat sequences untuk training
    X, y = [], []
    for i in range(sequence_length, len(scaled_data)):
        X.append(scaled_data[i-sequence_length:i, 0])
        y.append(scaled_data[i, 0])
    
    X, y = np.array(X), np.array(y)
    X = np.reshape(X, (X.shape[0], X.shape[1], 1))
    
    return X, y, scaler

class SimpleLSTM:
    """
    Implementasi LSTM sederhana menggunakan numpy
    Dioptimalkan untuk CPU dan mudah dipahami
    """
    def __init__(self, input_size=1, hidden_size=50, output_size=1, learning_rate=0.01):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        self.learning_rate = learning_rate

        # Inisialisasi weights secara random
        self.Wf = np.random.randn(hidden_size, input_size + hidden_size) * 0.1  # Forget gate
        self.Wi = np.random.randn(hidden_size, input_size + hidden_size) * 0.1  # Input gate
        self.Wo = np.random.randn(hidden_size, input_size + hidden_size) * 0.1  # Output gate
        self.Wc = np.random.randn(hidden_size, input_size + hidden_size) * 0.1  # Cell state

        # Bias
        self.bf = np.zeros((hidden_size, 1))
        self.bi = np.zeros((hidden_size, 1))
        self.bo = np.zeros((hidden_size, 1))
        self.bc = np.zeros((hidden_size, 1))

        # Output layer
        self.Wy = np.random.randn(output_size, hidden_size) * 0.1
        self.by = np.zeros((output_size, 1))

    def sigmoid(self, x):
        """Fungsi aktivasi sigmoid"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))

    def tanh(self, x):
        """Fungsi aktivasi tanh"""
        return np.tanh(np.clip(x, -500, 500))

    def forward(self, X):
        """Forward pass untuk satu sequence"""
        seq_len = X.shape[0]
        h = np.zeros((self.hidden_size, 1))
        c = np.zeros((self.hidden_size, 1))

        outputs = []

        for t in range(seq_len):
            x_t = X[t].reshape(-1, 1)

            # Concatenate input dan hidden state
            concat = np.vstack([x_t, h])

            # LSTM gates
            f_t = self.sigmoid(np.dot(self.Wf, concat) + self.bf)  # Forget gate
            i_t = self.sigmoid(np.dot(self.Wi, concat) + self.bi)  # Input gate
            o_t = self.sigmoid(np.dot(self.Wo, concat) + self.bo)  # Output gate
            c_tilde = self.tanh(np.dot(self.Wc, concat) + self.bc)  # Candidate values

            # Update cell state dan hidden state
            c = f_t * c + i_t * c_tilde
            h = o_t * self.tanh(c)

            outputs.append(h.copy())

        # Output layer
        y = np.dot(self.Wy, h) + self.by
        return y, outputs

    def train_step(self, X, y_true):
        """Satu step training dengan backpropagation sederhana"""
        y_pred, _ = self.forward(X)
        loss = np.mean((y_pred - y_true) ** 2)

        # Gradient descent sederhana (simplified)
        error = y_pred - y_true

        # Update output weights
        self.Wy -= self.learning_rate * np.dot(error, _.T) / len(X)
        self.by -= self.learning_rate * error

        return loss

    def predict(self, X):
        """Prediksi untuk satu sequence"""
        y_pred, _ = self.forward(X)
        return y_pred

def train_simple_model(X, y, sequence_length, epochs, batch_size):
    """
    Training model LSTM sederhana dengan implementasi numpy
    """
    # Split data training dan testing (80:20)
    train_size = int(len(X) * 0.8)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    # Buat model LSTM sederhana
    model = SimpleLSTM(input_size=1, hidden_size=20, output_size=1, learning_rate=0.001)

    # Training dengan progress bar
    progress_bar = st.progress(0)
    status_text = st.empty()

    losses = []

    for epoch in range(epochs):
        epoch_losses = []

        # Training batch by batch
        for i in range(0, len(X_train), batch_size):
            batch_X = X_train[i:i+batch_size]
            batch_y = y_train[i:i+batch_size]

            batch_loss = 0
            for j in range(len(batch_X)):
                # Reshape untuk LSTM input
                seq = batch_X[j].reshape(-1, 1)
                target = batch_y[j].reshape(-1, 1)

                loss = model.train_step(seq, target)
                batch_loss += loss

            epoch_losses.append(batch_loss / len(batch_X))

        avg_loss = np.mean(epoch_losses)
        losses.append(avg_loss)

        # Update progress
        progress = (epoch + 1) / epochs
        progress_bar.progress(progress)
        status_text.text(f'Epoch {epoch+1}/{epochs} - Loss: {avg_loss:.6f}')

    progress_bar.empty()
    status_text.empty()

    return model, X_test, y_test, losses

def make_predictions(model, X_test, scaler):
    """
    Membuat prediksi menggunakan model LSTM sederhana
    """
    predictions = []

    for i in range(len(X_test)):
        # Reshape untuk input LSTM
        seq = X_test[i].reshape(-1, 1)
        pred = model.predict(seq)
        predictions.append(pred[0, 0])

    predictions = np.array(predictions).reshape(-1, 1)
    predictions = scaler.inverse_transform(predictions)
    return predictions

def calculate_rmse(actual, predicted):
    """
    Menghitung Root Mean Square Error untuk evaluasi model
    """
    return np.sqrt(mean_squared_error(actual, predicted))

def generate_signal(actual_prices, predicted_prices):
    """
    Generate sinyal BUY/SELL berdasarkan prediksi
    BUY jika prediksi naik, SELL jika prediksi turun
    """
    if len(predicted_prices) < 2:
        return "HOLD"
    
    # Bandingkan harga terakhir dengan prediksi
    last_actual = actual_prices[-1]
    last_predicted = predicted_prices[-1][0]
    
    if last_predicted > last_actual * 1.02:  # Naik > 2%
        return "🟢 BUY"
    elif last_predicted < last_actual * 0.98:  # Turun > 2%
        return "🔴 SELL"
    else:
        return "🟡 HOLD"

# Main aplikasi
if any(selected_coins.values()):
    st.header("📊 Hasil Prediksi")
    
    for symbol, is_selected in selected_coins.items():
        if is_selected:
            coin_name = [k for k, v in COINS.items() if v == symbol][0]
            
            st.subheader(f"📈 {coin_name}")
            
            # Ambil data
            with st.spinner(f'Mengambil data {coin_name}...'):
                data = get_crypto_data(symbol)
            
            if data is not None and len(data) > SEQUENCE_LENGTH:
                # Preprocessing
                X, y, scaler = preprocess_data(data, SEQUENCE_LENGTH)
                
                if len(X) > 0:
                    # Training model
                    model, X_test, y_test, losses = train_simple_model(
                        X, y, SEQUENCE_LENGTH, EPOCHS, BATCH_SIZE
                    )
                    
                    # Prediksi
                    predictions = make_predictions(model, X_test, scaler)
                    y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1))
                    
                    # Hitung RMSE
                    rmse = calculate_rmse(y_test_actual, predictions)
                    
                    # Generate sinyal
                    signal = generate_signal(data['Close'].values, predictions)
                    
                    # Tampilkan hasil
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Harga Terakhir", f"${data['Close'].iloc[-1]:.2f}")
                    
                    with col2:
                        st.metric("RMSE", f"${rmse:.2f}")
                    
                    with col3:
                        st.metric("Sinyal", signal)
                    
                    # Plot grafik
                    fig, ax = plt.subplots(figsize=(12, 6))
                    
                    # Plot data testing actual vs prediksi
                    test_dates = data.index[-len(y_test_actual):]
                    ax.plot(test_dates, y_test_actual, label='Harga Aktual', color='blue', linewidth=2)
                    ax.plot(test_dates, predictions, label='Prediksi', color='red', linewidth=2, linestyle='--')
                    
                    ax.set_title(f'Prediksi Harga {coin_name}')
                    ax.set_xlabel('Tanggal')
                    ax.set_ylabel('Harga (USD)')
                    ax.legend()
                    ax.grid(True, alpha=0.3)
                    
                    st.pyplot(fig)
                    
                    # Tampilkan training history
                    if st.checkbox(f"Tampilkan Training Loss {coin_name}"):
                        fig2, ax1 = plt.subplots(1, 1, figsize=(10, 4))

                        ax1.plot(losses, label='Training Loss', color='orange')
                        ax1.set_title('Model Training Loss')
                        ax1.set_xlabel('Epoch')
                        ax1.set_ylabel('Loss')
                        ax1.legend()
                        ax1.grid(True, alpha=0.3)

                        st.pyplot(fig2)
                    
                    st.markdown("---")
                
                else:
                    st.error(f"Data tidak cukup untuk {coin_name}")
            else:
                st.error(f"Gagal mengambil data untuk {coin_name}")

else:
    st.info("👈 Silakan pilih cryptocurrency di sidebar untuk memulai prediksi")
    
    # Tampilkan informasi aplikasi
    st.markdown("""
    ## 📋 Cara Penggunaan:
    1. **Pilih Koin**: Centang cryptocurrency yang ingin diprediksi di sidebar
    2. **Atur Parameter**: Sesuaikan window size, epochs, dan batch size
    3. **Tunggu Proses**: Model akan otomatis ditraining dan membuat prediksi
    4. **Lihat Hasil**: Grafik, sinyal trading, dan evaluasi model akan ditampilkan
    
    ## 🔧 Optimisasi untuk Core i3:
    - **Batch Size**: 32 (optimal untuk 16GB RAM)
    - **Epochs**: 50 (balance antara akurasi dan waktu training)
    - **Model**: LSTM sederhana 2 layer dengan dropout
    - **Cache**: Data di-cache untuk menghindari download berulang
    
    ## ⚠️ Disclaimer:
    Aplikasi ini hanya untuk tujuan edukasi. Prediksi tidak menjamin keuntungan trading.
    Selalu lakukan riset sendiri sebelum berinvestasi.
    """)
