import streamlit as st
import yfinance as yf
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Konfigurasi halaman Streamlit
st.set_page_config(
    page_title="Crypto Price Predictor",
    page_icon="₿",
    layout="wide"
)

# Judul aplikasi
st.title("🚀 Cryptocurrency Price Predictor with LSTM")
st.markdown("Prediksi harga cryptocurrency menggunakan model LSTM yang dioptimalkan untuk laptop Core i3")

# Sidebar untuk pemilihan koin
st.sidebar.header("Pilih Cryptocurrency")
st.sidebar.markdown("Centang koin yang ingin diprediksi:")

# Dictionary koin dengan simbol Yahoo Finance
COINS = {
    'Bitcoin (BTC)': 'BTC-USD',
    'Ethereum (ETH)': 'ETH-USD', 
    'Solana (SOL)': 'SOL-USD',
    'Cardano (ADA)': 'ADA-USD',
    'XRP (XRP)': 'XRP-USD'
}

# Checkbox untuk setiap koin
selected_coins = {}
for coin_name, symbol in COINS.items():
    selected_coins[symbol] = st.sidebar.checkbox(coin_name, value=False)

# Parameter model (dioptimalkan untuk Core i3)
st.sidebar.header("Parameter Model")
SEQUENCE_LENGTH = st.sidebar.slider("Window Size (hari)", 30, 90, 60)
EPOCHS = st.sidebar.slider("Training Epochs", 20, 100, 50)
BATCH_SIZE = st.sidebar.slider("Batch Size", 16, 64, 32)

@st.cache_data
def get_crypto_data(symbol, period="2y"):
    """
    Mengambil data historis cryptocurrency dari Yahoo Finance
    Cache digunakan untuk menghindari download berulang
    """
    try:
        ticker = yf.Ticker(symbol)
        data = ticker.history(period=period)
        return data
    except Exception as e:
        st.error(f"Error mengambil data {symbol}: {str(e)}")
        return None

def preprocess_data(data, sequence_length=60):
    """
    Preprocessing data:
    1. Normalisasi menggunakan MinMaxScaler
    2. Membuat sequences untuk LSTM (windowing)
    """
    # Ambil hanya kolom Close
    close_prices = data['Close'].values.reshape(-1, 1)
    
    # Normalisasi data ke range 0-1
    scaler = MinMaxScaler(feature_range=(0, 1))
    scaled_data = scaler.fit_transform(close_prices)
    
    # Membuat sequences untuk training
    X, y = [], []
    for i in range(sequence_length, len(scaled_data)):
        X.append(scaled_data[i-sequence_length:i, 0])
        y.append(scaled_data[i, 0])
    
    X, y = np.array(X), np.array(y)
    X = np.reshape(X, (X.shape[0], X.shape[1], 1))
    
    return X, y, scaler

class SimplePredictor:
    """
    Model prediksi sederhana menggunakan Moving Average dan Linear Regression
    Lebih stabil dan mudah dipahami daripada LSTM kompleks
    """
    def __init__(self, window_size=10):
        self.window_size = window_size
        self.weights = None
        self.bias = 0
        self.learning_rate = 0.001

    def moving_average(self, data, window):
        """Hitung moving average"""
        if len(data) < window:
            return np.mean(data)
        return np.mean(data[-window:])

    def create_features(self, sequence):
        """Buat features dari sequence data"""
        features = []

        # Moving averages dengan window berbeda
        for window in [3, 5, 10, 20]:
            if len(sequence) >= window:
                ma = self.moving_average(sequence, window)
                features.append(ma)
            else:
                features.append(sequence[-1] if len(sequence) > 0 else 0)

        # Trend features
        if len(sequence) >= 2:
            # Short term trend (last 3 days)
            short_trend = np.mean(np.diff(sequence[-3:])) if len(sequence) >= 3 else 0
            features.append(short_trend)

            # Long term trend (last 10 days)
            long_trend = np.mean(np.diff(sequence[-10:])) if len(sequence) >= 10 else 0
            features.append(long_trend)
        else:
            features.extend([0, 0])

        # Volatility (standard deviation)
        if len(sequence) >= 5:
            volatility = np.std(sequence[-5:])
            features.append(volatility)
        else:
            features.append(0)

        # Recent price
        features.append(sequence[-1] if len(sequence) > 0 else 0)

        return np.array(features)

    def fit(self, X, y):
        """Training model dengan gradient descent sederhana"""
        n_samples, n_features = X.shape[0], 8  # 8 features yang kita buat

        # Initialize weights
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0

        losses = []

        # Training loop
        for epoch in range(100):  # Fixed epochs untuk stabilitas
            epoch_loss = 0

            for i in range(n_samples):
                # Buat features dari sequence
                features = self.create_features(X[i].flatten())

                # Forward pass
                prediction = np.dot(features, self.weights) + self.bias

                # Calculate loss
                loss = (prediction - y[i]) ** 2
                epoch_loss += loss

                # Backward pass (gradient descent)
                error = prediction - y[i]

                # Update weights
                self.weights -= self.learning_rate * error * features
                self.bias -= self.learning_rate * error

            avg_loss = epoch_loss / n_samples
            losses.append(avg_loss)

            # Early stopping jika loss sudah konvergen
            if len(losses) > 10 and abs(losses[-1] - losses[-10]) < 1e-6:
                break

        return losses

    def predict(self, X):
        """Prediksi menggunakan model yang sudah ditraining"""
        if self.weights is None:
            raise ValueError("Model belum ditraining!")

        predictions = []

        for i in range(len(X)):
            features = self.create_features(X[i].flatten())
            prediction = np.dot(features, self.weights) + self.bias
            predictions.append(prediction)

        return np.array(predictions)

def train_simple_model(X, y, sequence_length, epochs, batch_size):
    """
    Training model prediksi sederhana dengan numpy
    """
    # Split data training dan testing (80:20)
    train_size = int(len(X) * 0.8)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    # Buat model prediksi sederhana
    model = SimplePredictor(window_size=10)

    # Training dengan progress bar
    progress_bar = st.progress(0)
    status_text = st.empty()

    try:
        # Training model
        status_text.text('Training model... Mohon tunggu')
        progress_bar.progress(0.3)

        losses = model.fit(X_train, y_train)

        progress_bar.progress(1.0)
        status_text.text('Training selesai!')

        # Clear progress indicators
        import time
        time.sleep(1)
        progress_bar.empty()
        status_text.empty()

        return model, X_test, y_test, losses

    except Exception as e:
        progress_bar.empty()
        status_text.empty()
        st.error(f"Error saat training: {str(e)}")
        return None, None, None, []

def make_predictions(model, X_test, scaler):
    """
    Membuat prediksi menggunakan model sederhana
    """
    if model is None:
        return np.array([]).reshape(-1, 1)

    try:
        predictions = model.predict(X_test)
        predictions = predictions.reshape(-1, 1)
        predictions = scaler.inverse_transform(predictions)
        return predictions
    except Exception as e:
        st.error(f"Error saat prediksi: {str(e)}")
        return np.array([]).reshape(-1, 1)

def calculate_rmse(actual, predicted):
    """
    Menghitung Root Mean Square Error untuk evaluasi model
    """
    return np.sqrt(mean_squared_error(actual, predicted))

def generate_signal(actual_prices, predicted_prices):
    """
    Generate sinyal BUY/SELL berdasarkan prediksi
    BUY jika prediksi naik, SELL jika prediksi turun
    """
    if len(predicted_prices) < 2:
        return "HOLD"
    
    # Bandingkan harga terakhir dengan prediksi
    last_actual = actual_prices[-1]
    last_predicted = predicted_prices[-1][0]
    
    if last_predicted > last_actual * 1.02:  # Naik > 2%
        return "🟢 BUY"
    elif last_predicted < last_actual * 0.98:  # Turun > 2%
        return "🔴 SELL"
    else:
        return "🟡 HOLD"

# Main aplikasi
if any(selected_coins.values()):
    st.header("📊 Hasil Prediksi")
    
    for symbol, is_selected in selected_coins.items():
        if is_selected:
            coin_name = [k for k, v in COINS.items() if v == symbol][0]
            
            st.subheader(f"📈 {coin_name}")
            
            # Ambil data
            with st.spinner(f'Mengambil data {coin_name}...'):
                data = get_crypto_data(symbol)
            
            if data is not None and len(data) > SEQUENCE_LENGTH:
                # Preprocessing
                X, y, scaler = preprocess_data(data, SEQUENCE_LENGTH)
                
                if len(X) > 0:
                    # Training model
                    model, X_test, y_test, losses = train_simple_model(
                        X, y, SEQUENCE_LENGTH, EPOCHS, BATCH_SIZE
                    )

                    if model is not None and X_test is not None:
                        # Prediksi
                        predictions = make_predictions(model, X_test, scaler)

                        if len(predictions) > 0:
                            y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1))

                            # Hitung RMSE
                            rmse = calculate_rmse(y_test_actual, predictions)

                            # Generate sinyal
                            signal = generate_signal(data['Close'].values, predictions)

                            # Tampilkan hasil
                            col1, col2, col3 = st.columns(3)

                            with col1:
                                st.metric("Harga Terakhir", f"${data['Close'].iloc[-1]:.2f}")

                            with col2:
                                st.metric("RMSE", f"${rmse:.2f}")

                            with col3:
                                st.metric("Sinyal", signal)
                    
                            # Plot grafik
                            try:
                                fig, ax = plt.subplots(figsize=(12, 6))

                                # Plot data testing actual vs prediksi
                                test_dates = data.index[-len(y_test_actual):]
                                ax.plot(test_dates, y_test_actual, label='Harga Aktual', color='blue', linewidth=2)
                                ax.plot(test_dates, predictions, label='Prediksi', color='red', linewidth=2, linestyle='--')

                                ax.set_title(f'Prediksi Harga {coin_name}')
                                ax.set_xlabel('Tanggal')
                                ax.set_ylabel('Harga (USD)')
                                ax.legend()
                                ax.grid(True, alpha=0.3)

                                st.pyplot(fig)

                                # Tampilkan training history
                                if len(losses) > 0 and st.checkbox(f"Tampilkan Training Loss {coin_name}"):
                                    fig2, ax1 = plt.subplots(1, 1, figsize=(10, 4))

                                    ax1.plot(losses, label='Training Loss', color='orange')
                                    ax1.set_title('Model Training Loss')
                                    ax1.set_xlabel('Iteration')
                                    ax1.set_ylabel('Loss')
                                    ax1.legend()
                                    ax1.grid(True, alpha=0.3)

                                    st.pyplot(fig2)

                            except Exception as e:
                                st.error(f"Error saat membuat grafik: {str(e)}")

                            st.markdown("---")
                        else:
                            st.error(f"Gagal membuat prediksi untuk {coin_name}")
                    else:
                        st.error(f"Gagal training model untuk {coin_name}")
                
                else:
                    st.error(f"Data tidak cukup untuk {coin_name}")
            else:
                st.error(f"Gagal mengambil data untuk {coin_name}")

else:
    st.info("👈 Silakan pilih cryptocurrency di sidebar untuk memulai prediksi")
    
    # Tampilkan informasi aplikasi
    st.markdown("""
    ## 📋 Cara Penggunaan:
    1. **Pilih Koin**: Centang cryptocurrency yang ingin diprediksi di sidebar
    2. **Atur Parameter**: Sesuaikan window size, epochs, dan batch size
    3. **Tunggu Proses**: Model akan otomatis ditraining dan membuat prediksi
    4. **Lihat Hasil**: Grafik, sinyal trading, dan evaluasi model akan ditampilkan
    
    ## 🔧 Optimisasi untuk Core i3:
    - **Model**: Prediksi sederhana dengan Moving Average + Linear Regression
    - **Features**: 8 fitur teknikal (MA, trend, volatilitas)
    - **Training**: Gradient descent dengan 100 iterasi maksimal
    - **Cache**: Data di-cache untuk menghindari download berulang
    
    ## ⚠️ Disclaimer:
    Aplikasi ini hanya untuk tujuan edukasi. Prediksi tidak menjamin keuntungan trading.
    Selalu lakukan riset sendiri sebelum berinvestasi.
    """)
