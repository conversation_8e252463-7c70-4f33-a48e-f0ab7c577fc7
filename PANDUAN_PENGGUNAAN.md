# 🚀 Panduan Lengkap Cryptocurrency Predictor

## 📋 Instalasi

### 1. Pastikan Python 3.13 Terinstall
Anda sudah memiliki Python 3.13 di: `C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13`

### 2. Install Dependencies
Buka Command Prompt atau PowerShell di folder aplikasi, lalu jalankan:

```bash
pip install streamlit yfinance numpy pandas matplotlib scikit-learn
```

**Catatan**: Kita tidak menggunakan TensorFlow karena tidak kompatibel dengan Python 3.13. Sebagai gantinya, aplikasi menggunakan implementasi LSTM dari scratch dengan numpy.

### 3. Jalankan Aplikasi
```bash
streamlit run app.py
```

Aplikasi akan terbuka di browser pada `http://localhost:8501`

## 🎯 Cara Menggunakan

### Step 1: Pilih Cryptocurrency
- Di sidebar kiri, centang koin yang ingin diprediksi:
  - Bitcoin (BTC)
  - Ethereum (ETH) 
  - Solana (SOL)
  - Cardano (ADA)
  - XRP (XRP)

### Step 2: Atur Parameter Model
- **Window Size**: <PERSON><PERSON><PERSON> hari historis (default: 60)
- **Training Epochs**: Jumlah iterasi training (default: 50)
- **Batch Size**: Ukuran batch training (default: 32)

### Step 3: Tunggu Proses Training
- Aplikasi akan otomatis download data 2 tahun terakhir
- Model LSTM akan ditraining (2-5 menit per koin)
- Progress bar akan menunjukkan kemajuan training

### Step 4: Lihat Hasil
- **Grafik**: Perbandingan harga aktual vs prediksi
- **Metrics**: Harga terakhir, RMSE, dan sinyal trading
- **Trading Signal**: BUY/SELL/HOLD berdasarkan prediksi

## 📊 Interpretasi Hasil

### Trading Signals:
- **🟢 BUY**: Prediksi harga naik > 2%
- **🔴 SELL**: Prediksi harga turun > 2%  
- **🟡 HOLD**: Perubahan prediksi < 2%

### RMSE (Root Mean Square Error):
- Mengukur rata-rata kesalahan prediksi dalam USD
- Semakin kecil RMSE, semakin akurat model
- Contoh: RMSE $100 berarti rata-rata error $100

### Grafik:
- **Garis Biru**: Harga aktual (historical)
- **Garis Merah Putus-putus**: Prediksi model
- Semakin dekat kedua garis, semakin baik model

## ⚙️ Optimisasi untuk Core i3 + 16GB RAM

### Parameter yang Sudah Dioptimalkan:
- **Hidden Size**: 20 units (ringan untuk CPU)
- **Batch Size**: 32 (optimal untuk 16GB RAM)
- **Epochs**: 50 (balance waktu vs akurasi)
- **Learning Rate**: 0.001 (stabil)

### Tips Penggunaan:
1. **Mulai dengan 1-2 koin** untuk testing awal
2. **Tutup aplikasi lain** saat training untuk performa optimal
3. **Monitor Task Manager** untuk memastikan tidak overload
4. **Gunakan epochs 30-50** untuk hasil terbaik

## 🔧 Troubleshooting

### Masalah Umum:

#### 1. "ModuleNotFoundError"
**Solusi**: Install ulang dependencies
```bash
pip install --upgrade streamlit yfinance numpy pandas matplotlib scikit-learn
```

#### 2. "Error mengambil data"
**Solusi**: 
- Periksa koneksi internet
- Coba lagi setelah beberapa menit (Yahoo Finance rate limit)

#### 3. Training terlalu lambat
**Solusi**:
- Kurangi epochs ke 30-40
- Kurangi batch size ke 16
- Training satu koin saja

#### 4. RAM habis
**Solusi**:
- Tutup aplikasi lain
- Kurangi batch size ke 16
- Training satu koin per waktu

### Error Handling:
Aplikasi sudah dilengkapi error handling untuk:
- Data download failures
- Model training errors  
- Memory overflow protection
- Invalid parameter values

## 📈 Tips Trading (Educational Only)

### Analisis Sinyal:
1. **Jangan hanya andalkan 1 sinyal**
2. **Perhatikan trend jangka panjang**
3. **Bandingkan dengan analisis teknikal lain**
4. **Gunakan risk management**

### Interpretasi RMSE:
- **RMSE < $50**: Model cukup akurat untuk koin stabil
- **RMSE $50-200**: Normal untuk koin volatile  
- **RMSE > $200**: Model kurang reliable

### Best Practices:
- **Backtest** model dengan data historis
- **Paper trading** sebelum real trading
- **Diversifikasi** portfolio
- **Set stop-loss** dan take-profit

## 🎓 Pembelajaran Lanjutan

### Memahami Kode:
1. **Baca LSTM_Explanation.md** untuk teori LSTM
2. **Eksperimen dengan parameter** untuk memahami impact
3. **Modifikasi kode** untuk fitur tambahan
4. **Bandingkan dengan model lain** (Moving Average, etc.)

### Pengembangan Selanjutnya:
- Tambah technical indicators (RSI, MACD, Bollinger Bands)
- Implementasi ensemble methods
- Real-time data streaming
- Portfolio optimization
- Risk management tools

## ⚠️ Disclaimer Penting

**PERINGATAN**: Aplikasi ini HANYA untuk tujuan edukasi dan pembelajaran machine learning.

### Risiko Trading Cryptocurrency:
- **Volatilitas Tinggi**: Harga bisa berubah drastis
- **Market Risk**: Bisa kehilangan seluruh investasi
- **Model Limitation**: Prediksi tidak 100% akurat
- **No Financial Advice**: Bukan saran investasi

### Rekomendasi:
- **Pelajari fundamental analysis**
- **Gunakan hanya uang yang siap hilang**
- **Konsultasi dengan financial advisor**
- **Lakukan riset mendalam sebelum investasi**

## 📞 Support

Jika mengalami masalah:
1. Periksa panduan troubleshooting di atas
2. Pastikan semua dependencies terinstall
3. Coba restart aplikasi
4. Periksa log error di terminal

Selamat belajar machine learning dan cryptocurrency! 🚀📊
