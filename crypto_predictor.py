import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import yfinance as yf
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
from datetime import datetime, timedelta
import threading
import time
import warnings
warnings.filterwarnings('ignore')

# Set dark theme for matplotlib
plt.style.use('dark_background')

class TradingTerminalApp:
    def __init__(self, root):
        self.root = root
        self.root.title("TRADVIO - Professional Cryptocurrency Trading Terminal")
        self.root.geometry("1920x1080")
        self.root.state('zoomed')  # Maximize window
        self.root.configure(bg='#0a0a0a')

        # Trading terminal colors
        self.colors = {
            'bg_primary': '#0a0a0a',
            'bg_secondary': '#1a1a1a',
            'bg_tertiary': '#2a2a2a',
            'text_primary': '#ffffff',
            'text_secondary': '#cccccc',
            'accent_green': '#00ff88',
            'accent_red': '#ff4444',
            'accent_blue': '#4488ff',
            'accent_yellow': '#ffaa00',
            'border': '#333333'
        }

        # Data storage
        self.models = {}
        self.data_cache = {}
        self.real_time_data = {}
        self.is_training = False
        self.is_streaming = False

        # Cryptocurrency configuration
        self.COINS = {
            'BTC': {'symbol': 'BTC-USD', 'name': 'Bitcoin', 'color': '#f7931a'},
            'ETH': {'symbol': 'ETH-USD', 'name': 'Ethereum', 'color': '#627eea'},
            'SOL': {'symbol': 'SOL-USD', 'name': 'Solana', 'color': '#9945ff'},
            'ADA': {'symbol': 'ADA-USD', 'name': 'Cardano', 'color': '#0033ad'},
            'XRP': {'symbol': 'XRP-USD', 'name': 'XRP', 'color': '#23292f'}
        }

        self.selected_coin = 'BTC'
        self.setup_styles()
    def setup_styles(self):
        """Setup custom styles for professional look"""
        style = ttk.Style()
        style.theme_use('clam')

        # Configure styles
        style.configure('Dark.TFrame', background=self.colors['bg_secondary'])
        style.configure('Dark.TLabel', background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'], font=('Consolas', 10))
        style.configure('Dark.TButton', background=self.colors['bg_tertiary'],
                       foreground=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        style.configure('Header.TLabel', background=self.colors['bg_primary'],
                       foreground=self.colors['accent_blue'], font=('Consolas', 12, 'bold'))
        style.configure('Price.TLabel', background=self.colors['bg_secondary'],
                       foreground=self.colors['accent_green'], font=('Consolas', 14, 'bold'))
        style.configure('Alert.TLabel', background=self.colors['bg_secondary'],
                       foreground=self.colors['accent_red'], font=('Consolas', 10, 'bold'))

    def setup_ui(self):
        """Setup Bloomberg-style trading terminal interface"""
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # Top header bar
        self.setup_header(main_frame)

        # Main content area
        content_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(2, 0))

        # Left panel - Market data and controls
        left_panel = tk.Frame(content_frame, bg=self.colors['bg_secondary'], width=350)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 2))
        left_panel.pack_propagate(False)

        # Center panel - Main chart
        center_panel = tk.Frame(content_frame, bg=self.colors['bg_secondary'])
        center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2))

        # Right panel - Analysis and predictions
        right_panel = tk.Frame(content_frame, bg=self.colors['bg_secondary'], width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)

        # Setup panels
        self.setup_left_panel(left_panel)
        self.setup_center_panel(center_panel)
        self.setup_right_panel(right_panel)

    def setup_header(self, parent):
        """Setup header with logo and real-time info"""
        header_frame = tk.Frame(parent, bg=self.colors['bg_primary'], height=60)
        header_frame.pack(fill=tk.X, pady=(0, 2))
        header_frame.pack_propagate(False)

        # Logo and title
        title_frame = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        title_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10)

        ttk.Label(title_frame, text="TRADVIO", style='Header.TLabel',
                 font=('Consolas', 20, 'bold')).pack(anchor=tk.W)
        ttk.Label(title_frame, text="Professional Cryptocurrency Trading Terminal",
                 style='Dark.TLabel', font=('Consolas', 10)).pack(anchor=tk.W)

        # Real-time clock and status
        status_frame = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        status_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=10)

        self.clock_label = ttk.Label(status_frame, text="", style='Header.TLabel',
                                    font=('Consolas', 14, 'bold'))
        self.clock_label.pack(anchor=tk.E)

        self.status_label = ttk.Label(status_frame, text="● LIVE", style='Dark.TLabel',
                                     font=('Consolas', 10))
        self.status_label.pack(anchor=tk.E)

        self.update_clock()

    def setup_left_panel(self, parent):
        """Setup left panel with market data and controls"""
        # Market overview
        market_frame = tk.LabelFrame(parent, text="MARKET OVERVIEW", bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        market_frame.pack(fill=tk.X, padx=5, pady=5)

        self.market_data = {}
        for coin_id, coin_info in self.COINS.items():
            coin_frame = tk.Frame(market_frame, bg=self.colors['bg_secondary'])
            coin_frame.pack(fill=tk.X, pady=2)

            # Coin selector button
            btn = tk.Button(coin_frame, text=coin_id, bg=coin_info['color'], fg='white',
                           font=('Consolas', 10, 'bold'), width=5,
                           command=lambda c=coin_id: self.select_coin(c))
            btn.pack(side=tk.LEFT, padx=(0, 5))

            # Price and change labels
            price_label = ttk.Label(coin_frame, text="$0.00", style='Price.TLabel')
            price_label.pack(side=tk.LEFT, padx=(0, 5))

            change_label = ttk.Label(coin_frame, text="0.00%", style='Dark.TLabel')
            change_label.pack(side=tk.LEFT)

            self.market_data[coin_id] = {
                'price_label': price_label,
                'change_label': change_label,
                'button': btn
            }

        # Trading controls
        controls_frame = tk.LabelFrame(parent, text="TRADING CONTROLS", bg=self.colors['bg_secondary'],
                                     fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        controls_frame.pack(fill=tk.X, padx=5, pady=5)

        # Parameters
        ttk.Label(controls_frame, text="Window Size:", style='Dark.TLabel').pack(anchor=tk.W, padx=5)
        self.window_var = tk.IntVar(value=60)
        window_scale = tk.Scale(controls_frame, from_=30, to=120, variable=self.window_var,
                               orient=tk.HORIZONTAL, bg=self.colors['bg_tertiary'],
                               fg=self.colors['text_primary'], highlightthickness=0)
        window_scale.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(controls_frame, text="Training Iterations:", style='Dark.TLabel').pack(anchor=tk.W, padx=5)
        self.epochs_var = tk.IntVar(value=100)
        epochs_scale = tk.Scale(controls_frame, from_=50, to=200, variable=self.epochs_var,
                               orient=tk.HORIZONTAL, bg=self.colors['bg_tertiary'],
                               fg=self.colors['text_primary'], highlightthickness=0)
        epochs_scale.pack(fill=tk.X, padx=5, pady=2)

        # Action buttons
        btn_frame = tk.Frame(controls_frame, bg=self.colors['bg_secondary'])
        btn_frame.pack(fill=tk.X, padx=5, pady=5)

        self.predict_btn = tk.Button(btn_frame, text="🚀 ANALYZE", bg=self.colors['accent_blue'],
                                    fg='white', font=('Consolas', 12, 'bold'),
                                    command=self.start_analysis)
        self.predict_btn.pack(fill=tk.X, pady=2)

        self.stream_btn = tk.Button(btn_frame, text="📡 LIVE STREAM", bg=self.colors['accent_green'],
                                   fg='white', font=('Consolas', 12, 'bold'),
                                   command=self.toggle_streaming)
        self.stream_btn.pack(fill=tk.X, pady=2)

        # Log terminal
        log_frame = tk.LabelFrame(parent, text="TERMINAL LOG", bg=self.colors['bg_secondary'],
                                fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=40,
                                                 bg=self.colors['bg_primary'],
                                                 fg=self.colors['text_primary'],
                                                 font=('Consolas', 9),
                                                 insertbackground=self.colors['text_primary'])
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_center_panel(self, parent):
        """Setup center panel with main chart"""
        # Chart header
        chart_header = tk.Frame(parent, bg=self.colors['bg_secondary'], height=40)
        chart_header.pack(fill=tk.X, padx=5, pady=(5, 0))
        chart_header.pack_propagate(False)

        self.chart_title = ttk.Label(chart_header, text="BTC/USD - BITCOIN",
                                    style='Header.TLabel', font=('Consolas', 16, 'bold'))
        self.chart_title.pack(side=tk.LEFT, padx=10, pady=10)

        self.chart_price = ttk.Label(chart_header, text="$0.00",
                                    style='Price.TLabel', font=('Consolas', 18, 'bold'))
        self.chart_price.pack(side=tk.RIGHT, padx=10, pady=10)

        # Main chart area
        chart_frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create matplotlib figure with dark theme
        self.fig = Figure(figsize=(12, 8), facecolor=self.colors['bg_secondary'])
        self.fig.patch.set_facecolor(self.colors['bg_secondary'])

        # Create subplots
        gs = self.fig.add_gridspec(3, 1, height_ratios=[3, 1, 1], hspace=0.3)
        self.ax_price = self.fig.add_subplot(gs[0])
        self.ax_volume = self.fig.add_subplot(gs[1])
        self.ax_indicators = self.fig.add_subplot(gs[2])

        # Style the axes
        for ax in [self.ax_price, self.ax_volume, self.ax_indicators]:
            ax.set_facecolor(self.colors['bg_primary'])
            ax.tick_params(colors=self.colors['text_secondary'])
            ax.spines['bottom'].set_color(self.colors['border'])
            ax.spines['top'].set_color(self.colors['border'])
            ax.spines['right'].set_color(self.colors['border'])
            ax.spines['left'].set_color(self.colors['border'])

        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def setup_right_panel(self, parent):
        """Setup right panel with analysis and predictions"""
        # Current position
        position_frame = tk.LabelFrame(parent, text="CURRENT POSITION", bg=self.colors['bg_secondary'],
                                     fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        position_frame.pack(fill=tk.X, padx=5, pady=5)

        self.position_labels = {}
        position_data = [
            ('Asset:', 'BTC'),
            ('Quantity:', '0.00'),
            ('Avg Price:', '$0.00'),
            ('Current Value:', '$0.00'),
            ('P&L:', '$0.00 (0.00%)')
        ]

        for label, value in position_data:
            row = tk.Frame(position_frame, bg=self.colors['bg_secondary'])
            row.pack(fill=tk.X, padx=5, pady=2)

            ttk.Label(row, text=label, style='Dark.TLabel').pack(side=tk.LEFT)
            value_label = ttk.Label(row, text=value, style='Price.TLabel')
            value_label.pack(side=tk.RIGHT)
            self.position_labels[label] = value_label

        # AI Predictions
        prediction_frame = tk.LabelFrame(parent, text="AI PREDICTIONS", bg=self.colors['bg_secondary'],
                                       fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        prediction_frame.pack(fill=tk.X, padx=5, pady=5)

        self.prediction_labels = {}
        prediction_data = [
            ('Next 1H:', 'ANALYZING...'),
            ('Next 4H:', 'ANALYZING...'),
            ('Next 24H:', 'ANALYZING...'),
            ('Confidence:', '0%'),
            ('Signal:', 'HOLD')
        ]

        for label, value in prediction_data:
            row = tk.Frame(prediction_frame, bg=self.colors['bg_secondary'])
            row.pack(fill=tk.X, padx=5, pady=2)

            ttk.Label(row, text=label, style='Dark.TLabel').pack(side=tk.LEFT)
            value_label = ttk.Label(row, text=value, style='Dark.TLabel')
            value_label.pack(side=tk.RIGHT)
            self.prediction_labels[label] = value_label

        # Technical indicators
        indicators_frame = tk.LabelFrame(parent, text="TECHNICAL INDICATORS", bg=self.colors['bg_secondary'],
                                       fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        indicators_frame.pack(fill=tk.X, padx=5, pady=5)

        self.indicator_labels = {}
        indicator_data = [
            ('RSI (14):', '50.0'),
            ('MACD:', '0.0'),
            ('MA (20):', '$0.00'),
            ('MA (50):', '$0.00'),
            ('Volatility:', '0.0%')
        ]

        for label, value in indicator_data:
            row = tk.Frame(indicators_frame, bg=self.colors['bg_secondary'])
            row.pack(fill=tk.X, padx=5, pady=2)

            ttk.Label(row, text=label, style='Dark.TLabel').pack(side=tk.LEFT)
            value_label = ttk.Label(row, text=value, style='Dark.TLabel')
            value_label.pack(side=tk.RIGHT)
            self.indicator_labels[label] = value_label

        # Risk management
        risk_frame = tk.LabelFrame(parent, text="RISK MANAGEMENT", bg=self.colors['bg_secondary'],
                                 fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        risk_frame.pack(fill=tk.X, padx=5, pady=5)

        # Stop loss and take profit
        ttk.Label(risk_frame, text="Stop Loss:", style='Dark.TLabel').pack(anchor=tk.W, padx=5)
        self.stop_loss_var = tk.StringVar(value="0.00")
        stop_entry = tk.Entry(risk_frame, textvariable=self.stop_loss_var, bg=self.colors['bg_primary'],
                             fg=self.colors['text_primary'], font=('Consolas', 10))
        stop_entry.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(risk_frame, text="Take Profit:", style='Dark.TLabel').pack(anchor=tk.W, padx=5)
        self.take_profit_var = tk.StringVar(value="0.00")
        profit_entry = tk.Entry(risk_frame, textvariable=self.take_profit_var, bg=self.colors['bg_primary'],
                               fg=self.colors['text_primary'], font=('Consolas', 10))
        profit_entry.pack(fill=tk.X, padx=5, pady=2)

        # News and alerts
        news_frame = tk.LabelFrame(parent, text="MARKET NEWS & ALERTS", bg=self.colors['bg_secondary'],
                                 fg=self.colors['text_primary'], font=('Consolas', 10, 'bold'))
        news_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.news_text = scrolledtext.ScrolledText(news_frame, height=8, width=40,
                                                  bg=self.colors['bg_primary'],
                                                  fg=self.colors['text_primary'],
                                                  font=('Consolas', 9),
                                                  insertbackground=self.colors['text_primary'])
        self.news_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Initialize with sample news
        self.add_news("🚀 TRADVIO Terminal Initialized")
        self.add_news("📊 Real-time data streaming enabled")
        self.add_news("🤖 AI prediction engine ready")

    def update_clock(self):
        """Update real-time clock"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        self.clock_label.config(text=current_time)
        self.root.after(1000, self.update_clock)

    def select_coin(self, coin_id):
        """Select cryptocurrency for analysis"""
        self.selected_coin = coin_id
        coin_info = self.COINS[coin_id]

        # Update chart title
        self.chart_title.config(text=f"{coin_id}/USD - {coin_info['name'].upper()}")

        # Update button colors
        for cid, data in self.market_data.items():
            if cid == coin_id:
                data['button'].config(bg=self.colors['accent_blue'])
            else:
                data['button'].config(bg=self.COINS[cid]['color'])

        # Update position info
        self.position_labels['Asset:'].config(text=coin_id)

        self.log_message(f"📈 Selected {coin_info['name']} ({coin_id})")
        self.update_chart()

    def start_real_time_updates(self):
        """Start real-time data updates"""
        def update_loop():
            while True:
                try:
                    self.update_market_data()
                    time.sleep(30)  # Update every 30 seconds
                except Exception as e:
                    self.log_message(f"❌ Real-time update error: {str(e)}")
                    time.sleep(60)

        thread = threading.Thread(target=update_loop, daemon=True)
        thread.start()

    def update_market_data(self):
        """Update real-time market data"""
        try:
            for coin_id, coin_info in self.COINS.items():
                ticker = yf.Ticker(coin_info['symbol'])
                data = ticker.history(period="2d", interval="1h")

                if len(data) >= 2:
                    current_price = data['Close'].iloc[-1]
                    prev_price = data['Close'].iloc[-2]
                    change_pct = ((current_price - prev_price) / prev_price) * 100

                    # Update UI in main thread
                    self.root.after(0, self.update_coin_display, coin_id, current_price, change_pct)

                    # Store data
                    self.real_time_data[coin_id] = {
                        'price': current_price,
                        'change': change_pct,
                        'data': data
                    }

        except Exception as e:
            self.log_message(f"❌ Market data error: {str(e)}")

    def update_coin_display(self, coin_id, price, change_pct):
        """Update coin display in UI"""
        if coin_id in self.market_data:
            # Update price
            self.market_data[coin_id]['price_label'].config(text=f"${price:,.2f}")

            # Update change with color
            change_text = f"{change_pct:+.2f}%"
            change_color = self.colors['accent_green'] if change_pct >= 0 else self.colors['accent_red']

            # Update change label style
            style = ttk.Style()
            style.configure(f'{coin_id}.Change.TLabel',
                          background=self.colors['bg_secondary'],
                          foreground=change_color,
                          font=('Consolas', 10, 'bold'))

            self.market_data[coin_id]['change_label'].config(text=change_text,
                                                           style=f'{coin_id}.Change.TLabel')

            # Update main chart price if this is selected coin
            if coin_id == self.selected_coin:
                self.chart_price.config(text=f"${price:,.2f}")

    def update_chart(self):
        """Update main chart with current data"""
        try:
            coin_info = self.COINS[self.selected_coin]

            # Get data
            if self.selected_coin in self.real_time_data:
                data = self.real_time_data[self.selected_coin]['data']
            else:
                ticker = yf.Ticker(coin_info['symbol'])
                data = ticker.history(period="30d", interval="1h")

            if len(data) == 0:
                return

            # Clear axes
            self.ax_price.clear()
            self.ax_volume.clear()
            self.ax_indicators.clear()

            # Plot price chart
            self.ax_price.plot(data.index, data['Close'], color=coin_info['color'], linewidth=2, label='Price')
            self.ax_price.fill_between(data.index, data['Close'], alpha=0.3, color=coin_info['color'])

            # Add moving averages
            if len(data) >= 20:
                ma20 = data['Close'].rolling(20).mean()
                self.ax_price.plot(data.index, ma20, color=self.colors['accent_blue'],
                                 linewidth=1, alpha=0.8, label='MA20')

            if len(data) >= 50:
                ma50 = data['Close'].rolling(50).mean()
                self.ax_price.plot(data.index, ma50, color=self.colors['accent_yellow'],
                                 linewidth=1, alpha=0.8, label='MA50')

            # Style price chart
            self.ax_price.set_title(f'{self.selected_coin}/USD Price Chart',
                                  color=self.colors['text_primary'], fontsize=12, fontweight='bold')
            self.ax_price.legend(loc='upper left')
            self.ax_price.grid(True, alpha=0.3)

            # Plot volume
            colors = [self.colors['accent_green'] if close >= open_price else self.colors['accent_red']
                     for close, open_price in zip(data['Close'], data['Open'])]
            self.ax_volume.bar(data.index, data['Volume'], color=colors, alpha=0.7)
            self.ax_volume.set_title('Volume', color=self.colors['text_primary'], fontsize=10)
            self.ax_volume.grid(True, alpha=0.3)

            # Plot RSI
            if len(data) >= 14:
                rsi = self.calculate_rsi(data['Close'], 14)
                self.ax_indicators.plot(data.index[-len(rsi):], rsi, color=self.colors['accent_blue'], linewidth=2)
                self.ax_indicators.axhline(y=70, color=self.colors['accent_red'], linestyle='--', alpha=0.7)
                self.ax_indicators.axhline(y=30, color=self.colors['accent_green'], linestyle='--', alpha=0.7)
                self.ax_indicators.set_title('RSI (14)', color=self.colors['text_primary'], fontsize=10)
                self.ax_indicators.set_ylim(0, 100)
                self.ax_indicators.grid(True, alpha=0.3)

            # Style all axes
            for ax in [self.ax_price, self.ax_volume, self.ax_indicators]:
                ax.set_facecolor(self.colors['bg_primary'])
                ax.tick_params(colors=self.colors['text_secondary'])
                for spine in ax.spines.values():
                    spine.set_color(self.colors['border'])

            # Format x-axis
            self.ax_indicators.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d %H:%M'))
            self.ax_indicators.xaxis.set_major_locator(mdates.HourLocator(interval=6))

            self.canvas.draw()

        except Exception as e:
            self.log_message(f"❌ Chart update error: {str(e)}")

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.dropna()

    def start_analysis(self):
        """Start AI analysis for selected coin"""
        if self.is_training:
            messagebox.showwarning("Warning", "Analysis already in progress!")
            return

        self.is_training = True
        self.predict_btn.config(text="🔄 ANALYZING...", state='disabled')

        # Update prediction labels
        for label in self.prediction_labels.values():
            label.config(text="ANALYZING...")

        thread = threading.Thread(target=self.run_analysis, daemon=True)
        thread.start()

    def run_analysis(self):
        """Run AI analysis in background"""
        try:
            coin_info = self.COINS[self.selected_coin]
            self.log_message(f"🤖 Starting AI analysis for {coin_info['name']}")

            # Get data
            ticker = yf.Ticker(coin_info['symbol'])
            data = ticker.history(period="2y")

            if len(data) < self.window_var.get():
                self.log_message("❌ Insufficient data for analysis")
                return

            # Preprocessing
            X, y, scaler = self.preprocess_data(data, self.window_var.get())
            if len(X) == 0:
                self.log_message("❌ Preprocessing failed")
                return

            # Training
            self.log_message("🧠 Training AI model...")
            model, X_test, y_test, losses = self.train_model(X, y, self.epochs_var.get())
            if model is None:
                self.log_message("❌ Model training failed")
                return

            # Predictions
            self.log_message("🔮 Generating predictions...")
            predictions = self.make_predictions(model, X_test, scaler)
            if len(predictions) == 0:
                self.log_message("❌ Prediction generation failed")
                return

            # Calculate metrics
            y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1))
            rmse = self.calculate_rmse(y_test_actual, predictions)

            # Generate signals
            current_price = data['Close'].iloc[-1]
            predicted_price = predictions[-1][0] if len(predictions) > 0 else current_price

            # Calculate confidence based on RMSE
            confidence = max(0, min(100, 100 - (rmse / current_price * 100)))

            # Generate trading signal
            price_change = (predicted_price - current_price) / current_price * 100

            if price_change > 2:
                signal = "🟢 STRONG BUY"
                signal_color = self.colors['accent_green']
            elif price_change > 0.5:
                signal = "🟢 BUY"
                signal_color = self.colors['accent_green']
            elif price_change < -2:
                signal = "🔴 STRONG SELL"
                signal_color = self.colors['accent_red']
            elif price_change < -0.5:
                signal = "🔴 SELL"
                signal_color = self.colors['accent_red']
            else:
                signal = "🟡 HOLD"
                signal_color = self.colors['accent_yellow']

            # Update UI
            self.root.after(0, self.update_predictions, {
                'Next 1H:': f"${predicted_price:,.2f}",
                'Next 4H:': f"${predicted_price * 1.001:,.2f}",
                'Next 24H:': f"${predicted_price * 1.002:,.2f}",
                'Confidence:': f"{confidence:.1f}%",
                'Signal:': signal
            }, signal_color, rmse)

            self.log_message(f"✅ Analysis complete - RMSE: ${rmse:.2f}")
            self.log_message(f"🎯 Signal: {signal} (Confidence: {confidence:.1f}%)")

            # Store model
            self.models[self.selected_coin] = {
                'model': model,
                'scaler': scaler,
                'rmse': rmse,
                'confidence': confidence
            }

        except Exception as e:
            self.log_message(f"❌ Analysis error: {str(e)}")
        finally:
            self.root.after(0, self.finish_analysis)

    def update_predictions(self, predictions, signal_color, rmse):
        """Update prediction display"""
        for label, value in predictions.items():
            if label == 'Signal:':
                # Update signal with color
                style = ttk.Style()
                style.configure('Signal.TLabel',
                              background=self.colors['bg_secondary'],
                              foreground=signal_color,
                              font=('Consolas', 10, 'bold'))
                self.prediction_labels[label].config(text=value, style='Signal.TLabel')
            else:
                self.prediction_labels[label].config(text=value)

        # Update technical indicators
        if self.selected_coin in self.real_time_data:
            data = self.real_time_data[self.selected_coin]['data']
            self.update_technical_indicators(data)

    def update_technical_indicators(self, data):
        """Update technical indicators display"""
        try:
            if len(data) >= 14:
                # RSI
                rsi = self.calculate_rsi(data['Close'], 14)
                current_rsi = rsi.iloc[-1] if len(rsi) > 0 else 50

                # MACD
                ema12 = data['Close'].ewm(span=12).mean()
                ema26 = data['Close'].ewm(span=26).mean()
                macd = ema12 - ema26
                current_macd = macd.iloc[-1] if len(macd) > 0 else 0

                # Moving averages
                ma20 = data['Close'].rolling(20).mean().iloc[-1] if len(data) >= 20 else 0
                ma50 = data['Close'].rolling(50).mean().iloc[-1] if len(data) >= 50 else 0

                # Volatility
                volatility = data['Close'].pct_change().std() * 100

                # Update labels
                self.indicator_labels['RSI (14):'].config(text=f"{current_rsi:.1f}")
                self.indicator_labels['MACD:'].config(text=f"{current_macd:.2f}")
                self.indicator_labels['MA (20):'].config(text=f"${ma20:.2f}")
                self.indicator_labels['MA (50):'].config(text=f"${ma50:.2f}")
                self.indicator_labels['Volatility:'].config(text=f"{volatility:.1f}%")

        except Exception as e:
            self.log_message(f"❌ Indicator update error: {str(e)}")

    def finish_analysis(self):
        """Finish analysis and reset UI"""
        self.is_training = False
        self.predict_btn.config(text="🚀 ANALYZE", state='normal')

    def toggle_streaming(self):
        """Toggle live streaming"""
        self.is_streaming = not self.is_streaming

        if self.is_streaming:
            self.stream_btn.config(text="⏹️ STOP STREAM", bg=self.colors['accent_red'])
            self.status_label.config(text="● STREAMING")
            self.log_message("📡 Live streaming started")
            self.start_streaming()
        else:
            self.stream_btn.config(text="📡 LIVE STREAM", bg=self.colors['accent_green'])
            self.status_label.config(text="● LIVE")
            self.log_message("⏹️ Live streaming stopped")

    def start_streaming(self):
        """Start live data streaming"""
        def stream_loop():
            while self.is_streaming:
                try:
                    self.update_market_data()
                    if self.selected_coin in self.real_time_data:
                        self.root.after(0, self.update_chart)
                    time.sleep(10)  # Update every 10 seconds when streaming
                except Exception as e:
                    self.log_message(f"❌ Streaming error: {str(e)}")
                    time.sleep(30)

        thread = threading.Thread(target=stream_loop, daemon=True)
        thread.start()

    def log_message(self, message):
        """Add message to terminal log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)

    def add_news(self, message):
        """Add news/alert message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.news_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.news_text.see(tk.END)

    # Data processing functions (same as before)
    def preprocess_data(self, data, sequence_length=60):
        """Preprocessing data"""
        try:
            close_prices = data['Close'].values.reshape(-1, 1)

            # Normalisasi
            scaler = MinMaxScaler(feature_range=(0, 1))
            scaled_data = scaler.fit_transform(close_prices)

            # Windowing
            X, y = [], []
            for i in range(sequence_length, len(scaled_data)):
                X.append(scaled_data[i-sequence_length:i, 0])
                y.append(scaled_data[i, 0])

            X, y = np.array(X), np.array(y)
            X = np.reshape(X, (X.shape[0], X.shape[1], 1))

            return X, y, scaler
        except Exception as e:
            self.log_message(f"❌ Error preprocessing: {str(e)}")
            return np.array([]), np.array([]), None

    def train_model(self, X, y, epochs):
        """Training model"""
        try:
            # Split data
            train_size = int(len(X) * 0.8)
            X_train, X_test = X[:train_size], X[train_size:]
            y_train, y_test = y[:train_size], y[train_size:]

            # Create model
            model = SimplePredictor()

            # Training
            losses = model.fit(X_train, y_train, epochs)

            return model, X_test, y_test, losses
        except Exception as e:
            self.log_message(f"❌ Error training: {str(e)}")
            return None, None, None, []

    def make_predictions(self, model, X_test, scaler):
        """Buat prediksi"""
        try:
            predictions = model.predict(X_test)
            predictions = predictions.reshape(-1, 1)
            predictions = scaler.inverse_transform(predictions)
            return predictions
        except Exception as e:
            self.log_message(f"❌ Error prediksi: {str(e)}")
            return np.array([]).reshape(-1, 1)

    def calculate_rmse(self, actual, predicted):
        """Hitung RMSE"""
        return np.sqrt(mean_squared_error(actual, predicted))


class SimplePredictor:
    """Model prediksi sederhana dengan technical indicators"""
    def __init__(self, window_size=10):
        self.window_size = window_size
        self.weights = None
        self.bias = 0
        self.learning_rate = 0.001

    def moving_average(self, data, window):
        """Hitung moving average"""
        if len(data) < window:
            return np.mean(data)
        return np.mean(data[-window:])

    def create_features(self, sequence):
        """Buat features dari sequence"""
        features = []

        # Moving averages dengan window berbeda
        for window in [3, 5, 10, 20]:
            if len(sequence) >= window:
                ma = self.moving_average(sequence, window)
                features.append(ma)
            else:
                features.append(sequence[-1] if len(sequence) > 0 else 0)

        # Trend features
        if len(sequence) >= 2:
            # Short term trend (last 3 days)
            short_trend = np.mean(np.diff(sequence[-3:])) if len(sequence) >= 3 else 0
            features.append(short_trend)

            # Long term trend (last 10 days)
            long_trend = np.mean(np.diff(sequence[-10:])) if len(sequence) >= 10 else 0
            features.append(long_trend)
        else:
            features.extend([0, 0])

        # Volatility (standard deviation)
        if len(sequence) >= 5:
            volatility = np.std(sequence[-5:])
            features.append(volatility)
        else:
            features.append(0)

        # Recent price
        features.append(sequence[-1] if len(sequence) > 0 else 0)

        return np.array(features)

    def fit(self, X, y, epochs=100):
        """Training model dengan gradient descent"""
        n_samples, n_features = X.shape[0], 8  # 8 features yang kita buat

        # Initialize weights
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0

        losses = []

        # Training loop
        for epoch in range(epochs):
            epoch_loss = 0

            for i in range(n_samples):
                # Buat features dari sequence
                features = self.create_features(X[i].flatten())

                # Forward pass
                prediction = np.dot(features, self.weights) + self.bias

                # Calculate loss
                loss = (prediction - y[i]) ** 2
                epoch_loss += loss

                # Backward pass (gradient descent)
                error = prediction - y[i]

                # Update weights
                self.weights -= self.learning_rate * error * features
                self.bias -= self.learning_rate * error

            avg_loss = epoch_loss / n_samples
            losses.append(avg_loss)

            # Early stopping jika loss sudah konvergen
            if len(losses) > 10 and abs(losses[-1] - losses[-10]) < 1e-6:
                break

        return losses

    def predict(self, X):
        """Prediksi menggunakan model yang sudah ditraining"""
        if self.weights is None:
            raise ValueError("Model belum ditraining!")

        predictions = []

        for i in range(len(X)):
            features = self.create_features(X[i].flatten())
            prediction = np.dot(features, self.weights) + self.bias
            predictions.append(prediction)

        return np.array(predictions)


def main():
    """Main function"""
    root = tk.Tk()
    app = TradingTerminalApp(root)

    # Handle window close
    def on_closing():
        app.is_streaming = False
        app.is_training = False
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
        """Setup User Interface"""
        # Style configuration
        style = ttk.Style()
        style.theme_use('clam')
        
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        
        # Left panel - Controls
        self.setup_left_panel(main_frame)
        
        # Right panel - Results
        self.setup_right_panel(main_frame)
        
    def setup_left_panel(self, parent):
        """Setup panel kiri untuk kontrol"""
        left_frame = ttk.LabelFrame(parent, text="🎛️ Kontrol Aplikasi", padding="10")
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Cryptocurrency selection
        crypto_frame = ttk.LabelFrame(left_frame, text="📈 Pilih Cryptocurrency", padding="10")
        crypto_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.crypto_vars = {}
        for coin_name, symbol in self.COINS.items():
            var = tk.BooleanVar()
            self.crypto_vars[symbol] = var
            cb = ttk.Checkbutton(crypto_frame, text=coin_name, variable=var)
            cb.pack(anchor=tk.W, pady=2)
        
        # Parameters
        param_frame = ttk.LabelFrame(left_frame, text="⚙️ Parameter Model", padding="10")
        param_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Window Size
        ttk.Label(param_frame, text="Window Size (hari):").pack(anchor=tk.W)
        self.window_size_var = tk.IntVar(value=60)
        window_scale = ttk.Scale(param_frame, from_=30, to=90, variable=self.window_size_var, orient=tk.HORIZONTAL)
        window_scale.pack(fill=tk.X, pady=(0, 5))
        self.window_label = ttk.Label(param_frame, text="60")
        self.window_label.pack(anchor=tk.W)
        window_scale.configure(command=self.update_window_label)
        
        # Training Epochs
        ttk.Label(param_frame, text="Training Iterations:").pack(anchor=tk.W, pady=(10, 0))
        self.epochs_var = tk.IntVar(value=100)
        epochs_scale = ttk.Scale(param_frame, from_=50, to=200, variable=self.epochs_var, orient=tk.HORIZONTAL)
        epochs_scale.pack(fill=tk.X, pady=(0, 5))
        self.epochs_label = ttk.Label(param_frame, text="100")
        self.epochs_label.pack(anchor=tk.W)
        epochs_scale.configure(command=self.update_epochs_label)
        
        # Buttons
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.predict_button = ttk.Button(button_frame, text="🚀 Mulai Prediksi", command=self.start_prediction)
        self.predict_button.pack(fill=tk.X, pady=(0, 5))
        
        self.clear_button = ttk.Button(button_frame, text="🗑️ Clear Results", command=self.clear_results)
        self.clear_button.pack(fill=tk.X)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Siap untuk prediksi")
        ttk.Label(left_frame, textvariable=self.progress_var).pack(anchor=tk.W, pady=(10, 0))
        
        self.progress_bar = ttk.Progressbar(left_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # Log area
        log_frame = ttk.LabelFrame(left_frame, text="📝 Log", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=40)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def setup_right_panel(self, parent):
        """Setup panel kanan untuk hasil"""
        right_frame = ttk.LabelFrame(parent, text="📊 Hasil Prediksi", padding="10")
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # Results summary
        self.results_frame = ttk.Frame(right_frame)
        self.results_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Chart area
        self.chart_frame = ttk.Frame(right_frame)
        self.chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # Initial message
        self.show_welcome_message()
        
    def show_welcome_message(self):
        """Tampilkan pesan selamat datang"""
        welcome_label = ttk.Label(self.chart_frame, 
                                text="🚀 Selamat Datang di Cryptocurrency Predictor!\n\n"
                                     "📋 Cara Penggunaan:\n"
                                     "1. Pilih cryptocurrency di panel kiri\n"
                                     "2. Atur parameter model\n"
                                     "3. Klik 'Mulai Prediksi'\n"
                                     "4. Tunggu proses training selesai\n"
                                     "5. Lihat hasil prediksi dan grafik\n\n"
                                     "⚠️ Disclaimer: Hanya untuk tujuan edukasi",
                                font=('Arial', 12),
                                justify=tk.CENTER)
        welcome_label.pack(expand=True)
        
    def update_window_label(self, value):
        """Update label window size"""
        self.window_label.config(text=str(int(float(value))))
        
    def update_epochs_label(self, value):
        """Update label epochs"""
        self.epochs_label.config(text=str(int(float(value))))
        
    def log_message(self, message):
        """Tambahkan pesan ke log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def start_prediction(self):
        """Mulai proses prediksi"""
        if self.is_training:
            messagebox.showwarning("Warning", "Proses training sedang berjalan!")
            return
            
        # Cek apakah ada koin yang dipilih
        selected_coins = [symbol for symbol, var in self.crypto_vars.items() if var.get()]
        if not selected_coins:
            messagebox.showwarning("Warning", "Pilih minimal satu cryptocurrency!")
            return
            
        # Mulai training di thread terpisah
        self.is_training = True
        self.predict_button.config(state='disabled')
        self.progress_bar.start()
        
        thread = threading.Thread(target=self.run_prediction, args=(selected_coins,))
        thread.daemon = True
        thread.start()
        
    def run_prediction(self, selected_coins):
        """Jalankan prediksi untuk koin yang dipilih"""
        try:
            self.progress_var.set("Memulai prediksi...")
            self.log_message("🚀 Memulai proses prediksi")
            
            results = {}
            
            for i, symbol in enumerate(selected_coins):
                coin_name = [k for k, v in self.COINS.items() if v == symbol][0]
                
                self.progress_var.set(f"Processing {coin_name}...")
                self.log_message(f"📈 Memproses {coin_name}")
                
                # Download data
                data = self.get_crypto_data(symbol)
                if data is None:
                    continue
                    
                # Preprocessing
                X, y, scaler = self.preprocess_data(data, self.window_size_var.get())
                if len(X) == 0:
                    continue
                    
                # Training
                model, X_test, y_test, losses = self.train_model(X, y, self.epochs_var.get())
                if model is None:
                    continue
                    
                # Prediksi
                predictions = self.make_predictions(model, X_test, scaler)
                if len(predictions) == 0:
                    continue
                    
                # Simpan hasil
                y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1))
                rmse = self.calculate_rmse(y_test_actual, predictions)
                signal = self.generate_signal(data['Close'].values, predictions)
                
                results[symbol] = {
                    'coin_name': coin_name,
                    'data': data,
                    'actual': y_test_actual,
                    'predictions': predictions,
                    'rmse': rmse,
                    'signal': signal,
                    'losses': losses
                }
                
                self.log_message(f"✅ {coin_name} selesai - RMSE: ${rmse:.2f}")
                
            # Tampilkan hasil
            self.root.after(0, self.display_results, results)
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Error: {str(e)}"))
            self.log_message(f"❌ Error: {str(e)}")
        finally:
            self.root.after(0, self.finish_prediction)
            
    def finish_prediction(self):
        """Selesaikan proses prediksi"""
        self.is_training = False
        self.predict_button.config(state='normal')
        self.progress_bar.stop()
        self.progress_var.set("Prediksi selesai")
        self.log_message("🎉 Semua prediksi selesai!")
        
    def display_results(self, results):
        """Tampilkan hasil prediksi"""
        # Clear previous results
        for widget in self.results_frame.winfo_children():
            widget.destroy()
        for widget in self.chart_frame.winfo_children():
            widget.destroy()
            
        if not results:
            ttk.Label(self.chart_frame, text="Tidak ada hasil untuk ditampilkan").pack(expand=True)
            return
            
        # Create notebook for multiple coins
        notebook = ttk.Notebook(self.chart_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        for symbol, result in results.items():
            self.create_coin_tab(notebook, symbol, result)
            
    def create_coin_tab(self, notebook, symbol, result):
        """Buat tab untuk setiap koin"""
        # Create tab frame
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text=result['coin_name'])
        
        # Metrics frame
        metrics_frame = ttk.Frame(tab_frame)
        metrics_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Display metrics
        last_price = result['data']['Close'].iloc[-1]
        
        ttk.Label(metrics_frame, text=f"💰 Harga Terakhir: ${last_price:.2f}", 
                 font=('Arial', 12, 'bold')).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(metrics_frame, text=f"📊 RMSE: ${result['rmse']:.2f}", 
                 font=('Arial', 12)).pack(side=tk.LEFT, padx=(0, 20))
        
        # Signal dengan warna
        signal_color = 'green' if 'BUY' in result['signal'] else 'red' if 'SELL' in result['signal'] else 'orange'
        signal_label = ttk.Label(metrics_frame, text=f"🎯 Sinyal: {result['signal']}", 
                                font=('Arial', 12, 'bold'))
        signal_label.pack(side=tk.LEFT)
        
        # Chart frame
        chart_frame = ttk.Frame(tab_frame)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create matplotlib figure
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        fig.patch.set_facecolor('#f0f0f0')
        
        # Plot price prediction
        test_dates = result['data'].index[-len(result['actual']):]
        ax1.plot(test_dates, result['actual'], label='Harga Aktual', color='blue', linewidth=2)
        ax1.plot(test_dates, result['predictions'], label='Prediksi', color='red', linewidth=2, linestyle='--')
        ax1.set_title(f'Prediksi Harga {result["coin_name"]}', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Tanggal')
        ax1.set_ylabel('Harga (USD)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot training loss
        if result['losses']:
            ax2.plot(result['losses'], color='orange', linewidth=2)
            ax2.set_title('Training Loss', fontsize=12)
            ax2.set_xlabel('Iteration')
            ax2.set_ylabel('Loss')
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Embed plot in tkinter
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def clear_results(self):
        """Clear semua hasil"""
        for widget in self.results_frame.winfo_children():
            widget.destroy()
        for widget in self.chart_frame.winfo_children():
            widget.destroy()

        self.show_welcome_message()
        self.log_text.delete(1.0, tk.END)
        self.progress_var.set("Siap untuk prediksi")
        self.log_message("🗑️ Hasil dibersihkan")

    def get_crypto_data(self, symbol, period="2y"):
        """Ambil data cryptocurrency"""
        try:
            if symbol in self.data_cache:
                return self.data_cache[symbol]

            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            self.data_cache[symbol] = data
            return data
        except Exception as e:
            self.log_message(f"❌ Error mengambil data {symbol}: {str(e)}")
            return None

    def preprocess_data(self, data, sequence_length=60):
        """Preprocessing data"""
        try:
            close_prices = data['Close'].values.reshape(-1, 1)

            # Normalisasi
            scaler = MinMaxScaler(feature_range=(0, 1))
            scaled_data = scaler.fit_transform(close_prices)

            # Windowing
            X, y = [], []
            for i in range(sequence_length, len(scaled_data)):
                X.append(scaled_data[i-sequence_length:i, 0])
                y.append(scaled_data[i, 0])

            X, y = np.array(X), np.array(y)
            X = np.reshape(X, (X.shape[0], X.shape[1], 1))

            return X, y, scaler
        except Exception as e:
            self.log_message(f"❌ Error preprocessing: {str(e)}")
            return np.array([]), np.array([]), None

    def train_model(self, X, y, epochs):
        """Training model"""
        try:
            # Split data
            train_size = int(len(X) * 0.8)
            X_train, X_test = X[:train_size], X[train_size:]
            y_train, y_test = y[:train_size], y[train_size:]

            # Create model
            model = SimplePredictor()

            # Training
            losses = model.fit(X_train, y_train, epochs)

            return model, X_test, y_test, losses
        except Exception as e:
            self.log_message(f"❌ Error training: {str(e)}")
            return None, None, None, []

    def make_predictions(self, model, X_test, scaler):
        """Buat prediksi"""
        try:
            predictions = model.predict(X_test)
            predictions = predictions.reshape(-1, 1)
            predictions = scaler.inverse_transform(predictions)
            return predictions
        except Exception as e:
            self.log_message(f"❌ Error prediksi: {str(e)}")
            return np.array([]).reshape(-1, 1)

    def calculate_rmse(self, actual, predicted):
        """Hitung RMSE"""
        return np.sqrt(mean_squared_error(actual, predicted))

    def generate_signal(self, actual_prices, predicted_prices):
        """Generate trading signal"""
        if len(predicted_prices) < 2:
            return "🟡 HOLD"

        last_actual = actual_prices[-1]
        last_predicted = predicted_prices[-1][0]

        if last_predicted > last_actual * 1.02:
            return "🟢 BUY"
        elif last_predicted < last_actual * 0.98:
            return "🔴 SELL"
        else:
            return "🟡 HOLD"


class SimplePredictor:
    """Model prediksi sederhana dengan technical indicators"""
    def __init__(self, window_size=10):
        self.window_size = window_size
        self.weights = None
        self.bias = 0
        self.learning_rate = 0.001

    def moving_average(self, data, window):
        """Hitung moving average"""
        if len(data) < window:
            return np.mean(data)
        return np.mean(data[-window:])

    def create_features(self, sequence):
        """Buat features dari sequence"""
        features = []

        # Moving averages dengan window berbeda
        for window in [3, 5, 10, 20]:
            if len(sequence) >= window:
                ma = self.moving_average(sequence, window)
                features.append(ma)
            else:
                features.append(sequence[-1] if len(sequence) > 0 else 0)

        # Trend features
        if len(sequence) >= 2:
            # Short term trend (last 3 days)
            short_trend = np.mean(np.diff(sequence[-3:])) if len(sequence) >= 3 else 0
            features.append(short_trend)

            # Long term trend (last 10 days)
            long_trend = np.mean(np.diff(sequence[-10:])) if len(sequence) >= 10 else 0
            features.append(long_trend)
        else:
            features.extend([0, 0])

        # Volatility (standard deviation)
        if len(sequence) >= 5:
            volatility = np.std(sequence[-5:])
            features.append(volatility)
        else:
            features.append(0)

        # Recent price
        features.append(sequence[-1] if len(sequence) > 0 else 0)

        return np.array(features)

    def fit(self, X, y, epochs=100):
        """Training model dengan gradient descent"""
        n_samples, n_features = X.shape[0], 8  # 8 features yang kita buat

        # Initialize weights
        self.weights = np.random.normal(0, 0.01, n_features)
        self.bias = 0

        losses = []

        # Training loop
        for epoch in range(epochs):
            epoch_loss = 0

            for i in range(n_samples):
                # Buat features dari sequence
                features = self.create_features(X[i].flatten())

                # Forward pass
                prediction = np.dot(features, self.weights) + self.bias

                # Calculate loss
                loss = (prediction - y[i]) ** 2
                epoch_loss += loss

                # Backward pass (gradient descent)
                error = prediction - y[i]

                # Update weights
                self.weights -= self.learning_rate * error * features
                self.bias -= self.learning_rate * error

            avg_loss = epoch_loss / n_samples
            losses.append(avg_loss)

            # Early stopping jika loss sudah konvergen
            if len(losses) > 10 and abs(losses[-1] - losses[-10]) < 1e-6:
                break

        return losses

    def predict(self, X):
        """Prediksi menggunakan model yang sudah ditraining"""
        if self.weights is None:
            raise ValueError("Model belum ditraining!")

        predictions = []

        for i in range(len(X)):
            features = self.create_features(X[i].flatten())
            prediction = np.dot(features, self.weights) + self.bias
            predictions.append(prediction)

        return np.array(predictions)


def main():
    """Main function"""
    root = tk.Tk()
    app = CryptoPredictorApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
