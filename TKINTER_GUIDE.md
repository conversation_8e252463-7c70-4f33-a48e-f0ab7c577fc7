# 🖥️ Cryptocurrency Predictor - Tkinter Desktop Version

## 🎯 Overview

Aplikasi desktop cryptocurrency predictor menggunakan Tkinter dengan interface yang user-friendly dan fitur lengkap untuk prediksi harga cryptocurrency.

## 🚀 Fitur Utama

### 📊 **Interface Desktop Native**
- **Modern UI**: Interface yang clean dan professional
- **Multi-tab Results**: Setiap cryptocurrency ditampilkan di tab terpisah
- **Real-time Progress**: Progress bar dan log real-time
- **Interactive Charts**: Grafik matplotlib terintegrasi

### 💰 **Cryptocurrency Support**
- Bitcoin (BTC)
- Ethereum (ETH)
- Solana (SOL)
- Cardano (ADA)
- XRP (XRP)

### 🔧 **Parameter Control**
- **Window Size**: 30-90 hari (default: 60)
- **Training Iterations**: 50-200 (default: 100)
- **Multi-selection**: Pilih multiple coins sekaligus

### 📈 **Advanced Analytics**
- **Price Prediction**: Grafik harga aktual vs prediksi
- **Training Loss**: Visualisasi konvergensi model
- **Trading Signals**: BUY/SELL/HOLD recommendations
- **RMSE Evaluation**: Akurasi model measurement

## 🛠️ Instalasi dan Setup

### 1. **Prerequisites**
```bash
# Dependencies yang diperlukan
pip install yfinance numpy pandas matplotlib scikit-learn
```

### 2. **Menjalankan Aplikasi**

#### **Opsi 1: Double-click Batch File**
```
run_tkinter_app.bat
```

#### **Opsi 2: Command Line**
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe crypto_predictor_tkinter.py
```

#### **Opsi 3: Python Command**
```bash
python crypto_predictor_tkinter.py
```

## 🎮 Cara Penggunaan

### **Step 1: Pilih Cryptocurrency**
- Centang checkbox untuk koin yang ingin diprediksi
- Bisa pilih multiple coins sekaligus
- Setiap coin akan diproses secara berurutan

### **Step 2: Atur Parameter**
- **Window Size**: Jumlah hari historis untuk analisis
- **Training Iterations**: Jumlah iterasi training model

### **Step 3: Mulai Prediksi**
- Klik tombol "🚀 Mulai Prediksi"
- Monitor progress di progress bar dan log area
- Tunggu hingga semua coin selesai diproses

### **Step 4: Analisis Hasil**
- Setiap coin akan muncul di tab terpisah
- Lihat metrics: Harga terakhir, RMSE, Trading Signal
- Analisis grafik prediksi vs harga aktual
- Review training loss untuk evaluasi model

## 📊 Interface Components

### **Panel Kiri - Kontrol**
```
🎛️ Kontrol Aplikasi
├── 📈 Pilih Cryptocurrency
│   ├── ☑️ Bitcoin (BTC)
│   ├── ☑️ Ethereum (ETH)
│   ├── ☑️ Solana (SOL)
│   ├── ☑️ Cardano (ADA)
│   └── ☑️ XRP (XRP)
├── ⚙️ Parameter Model
│   ├── Window Size: [30-90]
│   └── Training Iterations: [50-200]
├── 🚀 Mulai Prediksi
├── 🗑️ Clear Results
├── Progress Bar
└── 📝 Log Area
```

### **Panel Kanan - Hasil**
```
📊 Hasil Prediksi
├── Tab 1: Bitcoin (BTC)
│   ├── 💰 Harga Terakhir: $XX,XXX
│   ├── 📊 RMSE: $XXX
│   ├── 🎯 Sinyal: 🟢 BUY
│   ├── 📈 Grafik Prediksi
│   └── 📉 Training Loss
├── Tab 2: Ethereum (ETH)
└── ...
```

## 🔧 Technical Architecture

### **Main Components**

#### **CryptoPredictorApp Class**
```python
class CryptoPredictorApp:
    def __init__(self, root):
        # Initialize UI and variables
        
    def setup_ui(self):
        # Create interface components
        
    def start_prediction(self):
        # Start prediction in separate thread
        
    def display_results(self):
        # Show results in tabs
```

#### **SimplePredictor Model**
```python
class SimplePredictor:
    def create_features(self, sequence):
        # Extract 8 technical features
        
    def fit(self, X, y, epochs):
        # Train with gradient descent
        
    def predict(self, X):
        # Make predictions
```

### **Threading Architecture**
- **Main Thread**: UI updates dan user interaction
- **Worker Thread**: Data download, training, prediction
- **Thread Communication**: `root.after()` untuk UI updates

### **Data Flow**
```
Yahoo Finance API → Raw Data → Preprocessing → Feature Engineering → Model Training → Prediction → Visualization
```

## 📈 Model Features

### **Technical Indicators (8 Features)**
1. **Moving Average 3**: Short-term trend
2. **Moving Average 5**: Near-term trend  
3. **Moving Average 10**: Medium-term trend
4. **Moving Average 20**: Long-term trend
5. **Short Trend**: 3-day price change average
6. **Long Trend**: 10-day price change average
7. **Volatility**: 5-day standard deviation
8. **Recent Price**: Latest closing price

### **Training Algorithm**
```python
# Gradient Descent with Feature Engineering
for epoch in range(iterations):
    for sample in training_data:
        features = extract_features(sample)
        prediction = model.forward(features)
        loss = (prediction - actual)²
        
        # Update weights
        weights -= learning_rate * gradient
```

## 🎯 Trading Signals

### **Signal Logic**
- **🟢 BUY**: Prediksi naik > 2% dari harga terakhir
- **🔴 SELL**: Prediksi turun > 2% dari harga terakhir
- **🟡 HOLD**: Perubahan < 2%

### **Risk Management**
- Gunakan stop-loss dan take-profit
- Diversifikasi portfolio
- Jangan invest lebih dari yang mampu ditanggung

## 🔍 Performance Optimization

### **Memory Management**
- Data caching untuk menghindari download berulang
- Efficient numpy operations
- Automatic garbage collection

### **CPU Optimization**
- Threading untuk non-blocking UI
- Vectorized computations
- Early stopping untuk training

### **UI Responsiveness**
- Progress indicators
- Real-time logging
- Non-blocking operations

## 🐛 Troubleshooting

### **Common Issues**

#### **1. "Python was not found"**
**Solution**: 
- Gunakan `run_tkinter_app.bat`
- Atau gunakan full path Python

#### **2. "ModuleNotFoundError"**
**Solution**:
```bash
pip install yfinance numpy pandas matplotlib scikit-learn
```

#### **3. "Application not responding"**
**Solution**:
- Tunggu proses training selesai
- Jangan close aplikasi saat training
- Check log area untuk progress

#### **4. "No data available"**
**Solution**:
- Check internet connection
- Try again after few minutes
- Yahoo Finance might have rate limits

### **Performance Tips**
- Start with 1-2 coins untuk testing
- Use default parameters untuk hasil optimal
- Close other applications untuk free up memory

## 📱 UI Screenshots Description

### **Main Interface**
```
┌─────────────────────────────────────────────────────────────┐
│ 🚀 Cryptocurrency Price Predictor                          │
├─────────────────┬───────────────────────────────────────────┤
│ 🎛️ Kontrol      │ 📊 Hasil Prediksi                        │
│                 │                                           │
│ ☑️ Bitcoin      │ ┌─ Bitcoin ─┬─ Ethereum ─┬─ Solana ─┐    │
│ ☑️ Ethereum     │ │           │            │          │    │
│ ☐ Solana       │ │ 💰 $45,230 │            │          │    │
│ ☐ Cardano      │ │ 📊 $234    │            │          │    │
│ ☐ XRP          │ │ 🟢 BUY     │            │          │    │
│                 │ │            │            │          │    │
│ Window: [60]    │ │ [Chart]    │            │          │    │
│ Epochs: [100]   │ │            │            │          │    │
│                 │ └────────────┴────────────┴──────────┘    │
│ 🚀 Mulai        │                                           │
│ 🗑️ Clear        │                                           │
│                 │                                           │
│ Progress: ████  │                                           │
│                 │                                           │
│ 📝 Log:         │                                           │
│ [12:34] Start   │                                           │
│ [12:35] BTC OK  │                                           │
└─────────────────┴───────────────────────────────────────────┘
```

## ⚠️ Disclaimer

**PENTING**: Aplikasi ini hanya untuk tujuan edukasi dan pembelajaran machine learning.

### **Risiko Trading**
- Cryptocurrency sangat volatile
- Prediksi tidak menjamin profit
- Bisa kehilangan seluruh investasi
- Selalu lakukan riset sendiri

### **Rekomendasi**
- Gunakan untuk pembelajaran ML
- Paper trading sebelum real trading
- Konsultasi dengan financial advisor
- Diversifikasi investasi

## 🎓 Educational Value

### **Pembelajaran yang Didapat**
- **GUI Programming**: Tkinter advanced techniques
- **Threading**: Non-blocking operations
- **Data Visualization**: Matplotlib integration
- **Machine Learning**: Feature engineering dan model training
- **Financial Analysis**: Technical indicators

### **Next Steps**
- Tambah technical indicators lain
- Implement ensemble methods
- Add backtesting functionality
- Create portfolio optimization
- Integrate real-time data streaming

Selamat menggunakan aplikasi desktop cryptocurrency predictor! 🚀📊
